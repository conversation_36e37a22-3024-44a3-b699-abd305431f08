/**
 * @description 个人信息获取
 */
import { fetch } from '@/api/fetch.ts'
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'

/* 个人信息获取 */
export const NAME_CHAT_ROOM_LIST_ALL = {
  name: 'NAME_CHAT_ROOM_LIST_ALL',
  url: '/imConnector/chatRoom/pagerChatRoomList',
}

/**
 * @description 回包(只记录关键信息)
 */
export interface CHAT_ROOM_LIST_DATA {
  createTime?: number
  hot?: number
  isFollow?: boolean
  isPw?: boolean
  num?: string
  prettyRoomId?: string
  roomContent?: string
  roomId?: string
  roomPic?: string
  roomRole?: string
  roomTitle?: string
  s?: number
  tagContent?: string
  tagIcon?: string
  tagType?: number
  id?: string
}

/**
 * @description 入参
 */
interface CHAT_ROOM_LIST_ALL_PARAMS {
  page: number
  size: number
  tagContent?: string
  tagType?: number | string
}

/**
 * @description 回包(只记录关键信息)
 */
export interface CHAT_ROOM_LIST_ALL_RETURN {
  code: number
  data: {
    last?: boolean
    page?: number
    size?: number
    records: CHAT_ROOM_LIST_DATA[]
  }
  message: string
}

export const CHAT_ROOM_LIST_ALL = (
  params: CHAT_ROOM_LIST_ALL_PARAMS,
): Promise<CHAT_ROOM_LIST_ALL_RETURN> => {
  return fetch(NAME_CHAT_ROOM_LIST_ALL, HTTP_01, {
    headers: {
      token: myToken(),
    },
    params,
    type: 'GET',
  }) as Promise<CHAT_ROOM_LIST_ALL_RETURN>
}
