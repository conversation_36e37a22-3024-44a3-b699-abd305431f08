/**
 * @description 获取积分方式接口
 */
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'
import { fetch } from '@/api/fetch'

export const API_GET_POINTS_WAY = {
  name: 'API_GET_POINTS_WAY',
  url: '/points/getGoldCoinWay',
}

/**
 * @description 回包
 */
export interface GET_POINTS_WAY_RETURN {
  code: number
  message: string
  time: string
  data: Array<{
    type: 'LIVE_TIME' | 'ROOM_TRADING' | 'MY_TRADING' | 'INVITER_TRADING'
    reachStandardValue: number
    awardValue: number
    limitValue: number
  }>
}

export const GET_POINTS_WAY = (): Promise<GET_POINTS_WAY_RETURN> => {
  return fetch(API_GET_POINTS_WAY, HTTP_01, {
    headers: {
      token: myToken(),
    },
    type: 'GET',
  }) as Promise<GET_POINTS_WAY_RETURN>
}
