import { Flex, Tabs } from 'antd'
import { useEffect, useState } from 'react'
import { SvgIconProfileBack } from '@/imgs/icons'
import { usePrivy } from '@privy-io/react-auth'
import { useSolanaWallets } from '@privy-io/react-auth/solana'
import { useRoute } from '@/hooks/useRoute'
import { useLocation } from 'react-router-dom'
import { GET_USER_BIND_ACCOUNT } from '@/api/interface/GET_USER_BIND_ACCOUNT'
import { walletStore } from '@/store'
import i18n from '@/i18n'
import './WalletPage.css'
import SpotWalletContent from './SpotWalletContent'
import PerpsWalletContent from './PerpsWalletContent'

export function Component() {
  const { goBack, searchParams } = useRoute()
  const location = useLocation()
  const isInvestPath = location.pathname === '/investment'
  // 从 URL 参数中获取 tab，如果没有则默认为 spot
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'spot')
  const [currentAddress, setCurrentAddress] = useState('')
  const [addressCache, setAddressCache] = useState<{
    solana?: { address: string; connectorType: string | null }
    evm?: { address: string; connectorType: string | null }
  }>({})

  // 获取用户绑定的账户信息，优先从 walletStore 中获取
  // 处理移动设备视口高度
  useEffect(() => {
    // 设置视口高度变量
    const setViewportHeight = () => {
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)
    }

    // 初始设置
    setViewportHeight()

    // 监听窗口大小变化
    window.addEventListener('resize', setViewportHeight)
    window.addEventListener('orientationchange', setViewportHeight)

    // 清理函数
    return () => {
      window.removeEventListener('resize', setViewportHeight)
      window.removeEventListener('orientationchange', setViewportHeight)
    }
  }, [])

  useEffect(() => {
    const fetchAddress = async () => {
      try {
        // 从 walletStore 中获取地址
        const addressCache = walletStore.getAddressCache()
        const solanaAddress = walletStore.solanaAddress
        const evmAddress = walletStore.evmAddress

        // 如果 walletStore 中有地址，则使用它
        if (solanaAddress || evmAddress) {
          setAddressCache(addressCache)

          // 设置当前地址为EVM地址（优先）或Solana地址
          setCurrentAddress(evmAddress || solanaAddress || '')
          return
        }

        // 如果 walletStore 中没有地址，则从 API 中获取
        const res = await GET_USER_BIND_ACCOUNT()
        if (res.code === 200) {
          // 获取Solana和EVM地址及connector_type
          const solanaAccount = res.data.bindAccountList.find(
            (account) => account.type === 6,
          )
          const evmAccount = res.data.bindAccountList.find(
            (account) => account.type === 5,
          )

          // 将地址存储在 walletStore 中
          if (solanaAccount?.address) {
            walletStore.setSolanaAddress(
              solanaAccount.address,
              solanaAccount.connector_type || '',
            )
          }
          if (evmAccount?.address) {
            walletStore.setEvmAddress(
              evmAccount.address,
              evmAccount.connector_type || '',
            )
          }

          // 更新地址缓存
          setAddressCache(walletStore.getAddressCache())

          // 设置当前地址为EVM地址（优先）或Solana地址
          setCurrentAddress(evmAccount?.address || solanaAccount?.address || '')
        }
      } catch (error) {
        console.error('获取地址失败:', error)
      }
    }

    fetchAddress()
  }, [])

  const handleBack = () => {
    goBack()
  }

  return (
    <div className="h-screen overflow-y-auto bg-[#F5F5F5]">
      {!isInvestPath && (
        <Flex className="fixed left-0 top-0 z-10 w-full items-center bg-[#F8F9FB] px-4 py-3 md:left-16">
          <div className="cursor-pointer text-xl" onClick={handleBack}>
            <SvgIconProfileBack className="mr-2 size-8 md:size-10" />
          </div>
          <span className="ml-2 text-base font-bold md:text-lg">
            {i18n.t('wallet')}
          </span>
        </Flex>
      )}

      <div
        className={`h-full w-full overflow-y-auto ${
          !isInvestPath ? 'pt-[60px]' : ''
        }`}
      >
        {/* 添加标签切换 */}
        <div className="mt-3">
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            className="wallet-tabs px-4 md:px-20"
            items={[
              {
                key: 'spot',
                label: i18n.t('wallet_tab_spot_wallet'),
                children: null,
              },
              {
                key: 'perps',
                label: i18n.t('wallet_tab_perp_wallet'),
                children: null,
              },
            ]}
          />
        </div>
        <Flex
          className="px-2 pt-4 md:mb-10 md:ml-20 md:mr-20 md:px-20 md:pt-4"
          vertical={true}
        >
          {activeTab === 'spot' && (
            <SpotWalletContent
              currentAddress={currentAddress}
              addressCache={addressCache}
            />
          )}

          {activeTab === 'perps' && (
            <PerpsWalletContent currentAddress={currentAddress} />
          )}
        </Flex>
      </div>
    </div>
  )
}

Component.displayName = 'WalletPage'
export default Component
