import {
  ChatroomErrorCode,
  Mode,
  chatroomManager,
} from '@/managers/chatroomManager'
import { Flex, Modal } from 'antd'
import {
  createContext,
  useContext,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from 'react'
import { observer } from 'mobx-react-lite'
import { ChatRoomPageStore } from '@/pages/ChatRoom/store/ChatRoomPageStore'
import { useRoute } from '@/hooks/useRoute'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import i18n from '@/i18n'
import { BuildChatModeAndScreenShareing } from '@/pages/TradePage/module/BuildChatModeAndScreenShareing'
import { BuildChatModeOnly } from '@/pages/TradePage/module/BuildChatModeOnly'
import { BuildTradeModeAndScreenShareing } from '@/pages/TradePage/module/BuildTradeModeAndScreenShareing'
import { BuildTradeModeOnly } from '@/pages/TradePage/module/BuildTradeModeOnly'
import { BuildPerpModeAndScreenShareing } from '@/pages/TradePage/module/BuildPerpModeAndScreenShareing'
import { BuildPerpModeOnly } from '@/pages/TradePage/module/BuildPerpModeOnly'
import { IS_OPEN_ROOM_LOG } from '@/config'
import { ButtonBlack } from '@/components/Customize/Button'
import JoinRoomContent from '@/pages/ChatRoom/module/JoinLockRoomContent'
import { useUserInteracted } from '@/pages/ChatRoom/hooks/useUserInteracted'
import Danmu from '@/pages/ChatRoom/module/Danmu'
import PerpAndSpotHeader from './module/PerpAndSpotHeader'
import { useDevice } from '@/hooks/useDevice'

const store = new ChatRoomPageStore()
export const StoreContext = createContext<ChatRoomPageStore>(store)
export const useStore = () => useContext(StoreContext)

const TradeComponent = observer(() => {
  const { hasUserInteracted } = useUserInteracted()
  const { path } = useRoute()
  const { addQuery, query } = useRoute()
  const { toastErr, toastInfo } = useToastMessage()
  const { roomMode } = store
  const { isMobile } = useDevice()

  const hasJoined = useRef(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const [modeType, setModeType] = useState<Mode>(Mode.Trade)

  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible' && store.roomId) {
        try {
          const roomInfo = await chatroomManager.fetchRoomDetail(store.roomId)
          if (
            roomInfo &&
            roomInfo.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST
          ) {
            handleRoomClosedOrKicked()
          } else {
            console.log('Room status OK.')
          }
        } catch (error: any) {
          console.error('Error checking room status:', error)
          // 根据 SDK 返回的错误码判断
          if (error.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST) {
            handleRoomClosedOrKicked()
          }
        }
      }
    }

    const handleRoomClosedOrKicked = () => {
      toastInfo(i18n.t('this_room_is_closed'))
      store.leaveRoom()
      path('/')
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [store.roomId])

  const joinRoom = (roomId: string, password: string) => {
    store
      .joinRoom(roomId as string, password, true)
      .then((res) => {
        if (res.code === ChatroomErrorCode.PK_SUCCESS) {
          console.log(`refresh`)
        }
      })
      .catch((error) => {
        if (error && error.code === ChatroomErrorCode.RC_CHATROOM_USER_KICKED) {
          toastErr(i18n.t('you_have_been_blocked_and_cannot_join_the_room'))
          store.roomDetial.roomId = ''
          path('/')
          return
        }
        if (error && error.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST) {
          toastErr(i18n.t('this_room_is_closed'))
          path('/') // 房间不存在也要离开
          return
        }
        if (
          error &&
          error.code === ChatroomErrorCode.PK_PASSWORD_IS_INCORRECT
        ) {
          toastErr(i18n.t('password_incorrect'))
          path('/')
          return
        }
        toastErr(`${error.message}(${error.code})`)
        path('/') // 其他加入失败也离开
      })
  }

  useLayoutEffect(() => {
    if (query?.roomMode) {
      store.handleModeChanged(query.roomMode as Mode)
    }
  }, [])

  useEffect(() => {
    addQuery({ roomMode })
  }, [roomMode])

  const handleModeChange = (mode: Mode) => {
    setModeType(mode)
    addQuery({ roomMode: mode })
  }

  return (
    <StoreContext.Provider value={store}>
      <Danmu />
      <Flex
        ref={containerRef}
        className={`w-full bg-[##313131] text-white ${isMobile ? 'h-[calc(100dvh-60px)]' : 'h-dvh'}`}
        vertical={true}
      >
        <Flex className="mb-4 w-full border-[#313131] py-2 md:mb-0 md:border-b">
          <PerpAndSpotHeader
            onModeChange={handleModeChange}
            initialMode={modeType}
          />
        </Flex>
        <div
          className={`w-full overflow-hidden ${isMobile ? 'h-[calc(100dvh-124px)]' : 'h-[calc(100dvh-64px)]'}`}
        >
          {modeType === Mode.Trade && <BuildTradeModeOnly />}

          {/* 永续合约交易模式 - 非共享 */}
          {modeType === Mode.Perp && <BuildPerpModeOnly />}
        </div>
      </Flex>
    </StoreContext.Provider>
  )
})

export default TradeComponent
