import {
  ChatroomErrorCode,
  Mode,
  chatroomManager,
} from '@/managers/chatroomManager'
import { Flex, Modal } from 'antd'
import {
  createContext,
  useContext,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from 'react'
import { observer } from 'mobx-react-lite'
import { ChatRoomPageStore } from '@/pages/ChatRoom/store/ChatRoomPageStore'
import { useRoute } from '@/hooks/useRoute'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import i18n from '@/i18n'
import { BuildChatModeAndScreenShareing } from '@/pages/TradePage/module/BuildChatModeAndScreenShareing'
import { BuildChatModeOnly } from '@/pages/TradePage/module/BuildChatModeOnly'
import { BuildTradeModeAndScreenShareing } from '@/pages/TradePage/module/BuildTradeModeAndScreenShareing'
import { BuildTradeModeOnly } from '@/pages/TradePage/module/BuildTradeModeOnly'
import { BuildPerpModeAndScreenShareing } from '@/pages/TradePage/module/BuildPerpModeAndScreenShareing'
import { BuildPerpModeOnly } from '@/pages/TradePage/module/BuildPerpModeOnly'
import { IS_OPEN_ROOM_LOG } from '@/config'
import { ButtonBlack } from '@/components/Customize/Button'
import JoinRoomContent from '@/pages/ChatRoom/module/JoinLockRoomContent'
import { useUserInteracted } from '@/pages/ChatRoom/hooks/useUserInteracted'
import Danmu from '@/pages/ChatRoom/module/Danmu'
import PerpAndSpotHeader from './module/PerpAndSpotHeader'

const store = new ChatRoomPageStore()
export const StoreContext = createContext<ChatRoomPageStore>(store)
export const useStore = () => useContext(StoreContext)

const TradeComponent = observer(() => {
  const { hasUserInteracted } = useUserInteracted()
  const { path } = useRoute()
  const { addQuery, query } = useRoute()
  const { toastErr, toastInfo } = useToastMessage()
  const { roomMode } = store

  const hasJoined = useRef(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const [modeType, setModeType] = useState<Mode>(Mode.Trade)

  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible' && store.roomId) {
        try {
          const roomInfo = await chatroomManager.fetchRoomDetail(store.roomId)
          if (
            roomInfo &&
            roomInfo.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST
          ) {
            handleRoomClosedOrKicked()
          } else {
            console.log('Room status OK.')
          }
        } catch (error: any) {
          console.error('Error checking room status:', error)
          // 根据 SDK 返回的错误码判断
          if (error.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST) {
            handleRoomClosedOrKicked()
          }
        }
      }
    }

    const handleRoomClosedOrKicked = () => {
      toastInfo(i18n.t('this_room_is_closed'))
      store.leaveRoom()
      path('/')
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [store.roomId])

  const joinRoom = (roomId: string, password: string) => {
    store
      .joinRoom(roomId as string, password, true)
      .then((res) => {
        if (res.code === ChatroomErrorCode.PK_SUCCESS) {
          console.log(`refresh`)
        }
      })
      .catch((error) => {
        if (error && error.code === ChatroomErrorCode.RC_CHATROOM_USER_KICKED) {
          toastErr(i18n.t('you_have_been_blocked_and_cannot_join_the_room'))
          store.roomDetial.roomId = ''
          path('/')
          return
        }
        if (error && error.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST) {
          toastErr(i18n.t('this_room_is_closed'))
          path('/') // 房间不存在也要离开
          return
        }
        if (
          error &&
          error.code === ChatroomErrorCode.PK_PASSWORD_IS_INCORRECT
        ) {
          toastErr(i18n.t('password_incorrect'))
          path('/')
          return
        }
        toastErr(`${error.message}(${error.code})`)
        path('/') // 其他加入失败也离开
      })
  }

  // 加入房间
  useEffect(() => {
    if (!hasJoined.current) {
      const curRoomId = query?.roomId
      if (curRoomId) {
        if (!hasUserInteracted) {
          const curRoomIdFromLocal = store.getCurRoomId()
          if (curRoomIdFromLocal === curRoomId) {
            // 已经在房间内
            Modal.confirm({
              title: null,
              icon: null,
              content: (
                <Flex vertical className="items-center justify-center">
                  <Flex className="mb-4 text-center text-[20px] font-bold text-[#1A1B1E]">
                    {i18n.t('need_interaction')}
                  </Flex>
                  <Flex className="mb-4 text-center text-[14px] font-normal text-[#1A1B1E]">
                    {i18n.t('browsers_require_user_interaction')}
                  </Flex>
                  <ButtonBlack
                    onClick={() => {
                      const curPassword = store.getCurPassword()
                      joinRoom(curRoomId, curPassword ?? '')
                      Modal.destroyAll()
                    }}
                  >
                    {i18n.t('user_interaction_confirm')}
                  </ButtonBlack>
                </Flex>
              ),
              width: 500,
              okButtonProps: { style: { display: 'none' } },
              cancelButtonProps: { style: { display: 'none' } },
              footer: null,
              maskClosable: false,
              closable: false,
            })
          } else {
            Modal.confirm({
              title: null,
              icon: null,
              content: (
                <JoinRoomContent
                  roomId={curRoomId}
                  onJoinRoom={(password) => {
                    joinRoom(curRoomId, password)
                    Modal.destroyAll()
                  }}
                  onReturnHome={() => {
                    path('/')
                    Modal.destroyAll()
                  }}
                />
              ),
              width: 500,
              okButtonProps: { style: { display: 'none' } },
              cancelButtonProps: { style: { display: 'none' } },
              footer: null,
              maskClosable: false,
              closable: false,
            })
          }
        }
        IS_OPEN_ROOM_LOG &&
          console.log(`****** AudioRoomHeader curRoomId:${curRoomId} ******`)
      }
      hasJoined.current = true
    }
  }, [query?.roomId]) // 确保 query?.roomId 作为依赖

  useLayoutEffect(() => {
    if (query?.roomMode) {
      store.handleModeChanged(query.roomMode as Mode)
    }
  }, [])

  useEffect(() => {
    addQuery({ roomMode })
  }, [roomMode])

  const handleModeChange = (mode: Mode) => {
    setModeType(mode)
    addQuery({ roomMode: mode })
  }

  return (
    <StoreContext.Provider value={store}>
      <Danmu />
      <Flex
        ref={containerRef}
        className="h-dvh w-full bg-[##313131] text-white"
        vertical={true}
      >
        <Flex className="mb-4 w-full border-[#313131] py-2 md:mb-0 md:border-b">
          <PerpAndSpotHeader
            onModeChange={handleModeChange}
            initialMode={modeType}
          />
        </Flex>
        <div className="h-[calc(100dvh-64px)] w-full overflow-hidden">
          {modeType === Mode.Trade && <BuildTradeModeOnly />}

          {/* 永续合约交易模式 - 非共享 */}
          {modeType === Mode.Perp && <BuildPerpModeOnly />}
        </div>
      </Flex>
    </StoreContext.Provider>
  )
})

export default TradeComponent
