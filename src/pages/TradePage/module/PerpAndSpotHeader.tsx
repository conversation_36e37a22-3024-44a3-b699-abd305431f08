import { Flex } from 'antd'
import { observer } from 'mobx-react-lite'
import { Mode } from '@/managers/chatroomManager'
import clsx from 'clsx'
import { useState, useEffect } from 'react'

const PerpAndSpotHeader = observer(
  ({
    onModeChange,
    initialMode,
  }: {
    onModeChange?: (mode: Mode) => void
    initialMode?: Mode
  }) => {
    const [selectedMode, setSelectedMode] = useState<Mode>(
      initialMode || Mode.Trade,
    )

    useEffect(() => {
      if (initialMode) {
        setSelectedMode(initialMode)
      }
    }, [initialMode])

    const handleModeChange = (mode: Mode) => {
      setSelectedMode(mode)
      onModeChange?.(mode)
    }

    return (
      <Flex className="h-[40px] w-full items-center justify-start rounded-[8px] bg-[#292929] px-1 py-1">
        <Flex
          className={clsx(
            'h-full w-[100px] cursor-pointer items-center justify-center rounded-[6px] text-[14px] font-bold',
            selectedMode === Mode.Perp
              ? 'bg-[#1B1B1B] text-white'
              : 'text-[#979797] hover:text-[#AAAACBB]',
          )}
          onClick={() => handleModeChange(Mode.Perp)}
        >
          Perpetuals
        </Flex>
        <Flex
          className={clsx(
            'h-full w-[100px] cursor-pointer items-center justify-center rounded-[6px] text-[14px] font-bold',
            selectedMode === Mode.Trade
              ? 'bg-[#1B1B1B] text-white'
              : 'text-[#979797] hover:text-[#AAAACBB]',
          )}
          onClick={() => handleModeChange(Mode.Trade)}
        >
          Spot
        </Flex>
      </Flex>
    )
  },
)

export default PerpAndSpotHeader
