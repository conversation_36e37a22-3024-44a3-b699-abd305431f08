/* 底部弹出Modal样式 */
.related-live-mobile-modal .ant-modal {
  padding: 0 !important;
  margin: 0 !important;
  max-width: 100vw !important;
  width: 100vw !important;
  top: auto !important;
  bottom: 0 !important;
  transform: none !important;
}

.related-live-mobile-modal .ant-modal-content {
  padding: 0 !important;
  border-radius: 16px 16px 0 0 !important;
  background-color: #1A1B1E !important;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3) !important;
  max-height: 80vh !important;
}

.related-live-mobile-modal .ant-modal-body {
  padding: 0 !important;
  border-radius: 16px 16px 0 0 !important;
  background-color: #1A1B1E !important;
  max-height: 80vh !important;
}

/* 确保Modal从底部弹出 */
.related-live-mobile-modal {
  padding: 0 !important;
  margin: 0 !important;
}

.related-live-mobile-modal .ant-modal-wrap {
  padding: 0 !important;
  align-items: flex-end !important;
}

/* 弹出动画 */
.related-live-mobile-modal .ant-modal {
  animation: slideUpFromBottom 0.3s ease-out;
}

@keyframes slideUpFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
