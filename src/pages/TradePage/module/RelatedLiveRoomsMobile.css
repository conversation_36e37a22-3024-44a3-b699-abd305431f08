/* 底部弹出Modal样式 */
.related-live-mobile-modal {
  padding: 0 !important;
  margin: 0 !important;
}

.related-live-mobile-modal .ant-modal-wrap {
  padding: 0 !important;
  align-items: flex-end !important;
  justify-content: center !important;
  display: flex !important;
}

.related-live-mobile-modal .ant-modal {
  padding: 0 !important;
  margin: 0 !important;
  max-width: 100vw !important;
  width: 100vw !important;
  top: auto !important;
  bottom: 0 !important;
  position: relative !important;
  transform: none !important;
  animation: slideUpFromBottom 0.3s ease-out !important;
}

.related-live-mobile-modal .ant-modal-content {
  padding: 0 !important;
  border-radius: 16px 16px 0 0 !important;
  background-color: #1a1b1e !important;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3) !important;
  max-height: 80vh !important;
  margin: 0 !important;
  transform: none !important;
}

.related-live-mobile-modal .ant-modal-body {
  padding: 0 !important;
  border-radius: 16px 16px 0 0 !important;
  background-color: #1a1b1e !important;
  max-height: 80vh !important;
}

/* 从底部滑入动画 */
@keyframes slideUpFromBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 关闭时滑出动画 */
.related-live-mobile-modal.ant-modal-wrap-leave .ant-modal {
  animation: slideDownToBottom 0.3s ease-in !important;
}

@keyframes slideDownToBottom {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}
