import React, { useEffect, useState } from 'react'
import { Flex } from 'antd'
import {
  CHAT_ROOM_LIST_ALL,
  CHAT_ROOM_LIST_DATA,
} from '@/api/interface/CHAT_ROOM_LIST_ALL'
import { lodash } from '@/utils'
import InfiniteScroll from 'react-infinite-scroll-component'
import { RoomCard } from './RoomCard'
import i18n from '@/i18n'
import { tradeStore } from '@/pages/Trade/store/tradeStore'
import { perpsStore } from '@/store'
import { CloseOutlined } from '@ant-design/icons'
import { useRoute } from '@/hooks/useRoute'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { ChatroomErrorCode, chatroomManager } from '@/managers/chatroomManager'
import './RelatedLiveRoomsMobile.css'

interface RelatedLiveRoomsMobileProps {
  visible: boolean
  onClose: () => void
  modeType: 'trade' | 'perp'
}

export const RelatedLiveRoomsMobile: React.FC<RelatedLiveRoomsMobileProps> = ({
  visible,
  onClose,
  modeType,
}) => {
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [roomList, setRoomList] = useState<CHAT_ROOM_LIST_DATA[]>([])

  const loadAllRooms = async () => {
    if (loading) {
      return
    }
    setLoading(true)
    try {
      const res = await CHAT_ROOM_LIST_ALL({
        page: 1,
        size: 1000,
        tagContent:
          modeType == 'trade'
            ? tradeStore.currCrypto?.symbol
            : modeType == 'perp'
              ? perpsStore.init.coin
              : '',
        // tagType: modeType == 'trade' ? 1 : modeType == 'perp' ? 4 : '',
      })
      setLoading(false)
      if (res?.code !== 200) return
      const newRecords = res.data.records
      const mergedRecords = lodash.uniqBy([...roomList, ...newRecords], 'id')
      setRoomList(mergedRecords)
      setHasMore(res.data.last !== true)
    } catch (error) {
      console.error('Failed to load rooms:', error)
      setLoading(false)
    }
  }

  useEffect(() => {
    if (visible) {
      setRoomList([]) // 重置房间列表
      loadAllRooms()
    }
  }, [visible, modeType])

  if (!visible) return null

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center">
      {/* 背景遮罩 */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />

      {/* 弹窗内容 */}
      <div
        className={`relative max-h-[85vh] w-full transform rounded-t-[16px] bg-[#1A1B1E] shadow-lg transition-transform duration-300 ease-out ${
          visible ? 'translate-y-0' : 'translate-y-full'
        }`}
        style={{
          animation: visible
            ? 'slideUpFromBottom 0.3s ease-out'
            : 'slideDownToBottom 0.3s ease-in',
          marginBottom: '0px',
        }}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between rounded-t-[16px] border-b border-gray-700 bg-[#1A1B1E] px-4 py-4">
          <div className="text-lg font-bold text-white">Related Live</div>
          <button
            onClick={onClose}
            className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2A2A2A] transition-colors hover:bg-[#3A3A3A]"
          >
            <CloseOutlined className="text-white" />
          </button>
        </div>

        {/* 内容区域 */}
        <div
          className="overflow-hidden"
          style={{ maxHeight: 'calc(85vh - 64px)' }}
        >
          <div
            id="related-live-mobile-scrollable-div"
            className="w-full overflow-y-auto overflow-x-hidden p-4"
            style={{ maxHeight: 'calc(85vh - 64px)' }}
          >
            {roomList.length === 0 && !loading ? (
              <Flex className="h-32 items-center justify-center">
                <div className="font-lexend text-gray-400">暂无相关直播</div>
              </Flex>
            ) : (
              <InfiniteScroll
                dataLength={roomList.length}
                next={loadAllRooms}
                hasMore={hasMore}
                loader={
                  <Flex className="items-center justify-center">
                    <div className="pt-2 font-lexend text-gray-400">
                      {i18n.t('loading')}
                    </div>
                  </Flex>
                }
                scrollableTarget="related-live-mobile-scrollable-div"
              >
                <div className="grid grid-cols-1 gap-4">
                  {roomList.map((item) => (
                    <RoomCardMobile
                      key={item.id}
                      item={item}
                      onRoomClosed={(roomId) => {
                        setRoomList((prev) =>
                          prev.filter((room) => room.roomId !== roomId),
                        )
                      }}
                    />
                  ))}
                </div>
              </InfiniteScroll>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// 移动端房间卡片组件
const RoomCardMobile: React.FC<{
  item: CHAT_ROOM_LIST_DATA
  onRoomClosed?: (roomId: string) => void
}> = ({ item, onRoomClosed }) => {
  const { path } = useRoute()
  const { toastErr } = useToastMessage()
  const [isJoining, setIsJoining] = useState(false)

  const handleItemClicked = () => {
    if (isJoining) return

    setIsJoining(true)
    chatroomManager
      .fetchRoomDetail(item.roomId!, '', false)
      .then((res) => {
        if (res?.code === ChatroomErrorCode.PK_SUCCESS) {
          path(`/chatroom?roomId=${item.roomId}`)
        } else {
          if (res?.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST) {
            toastErr(i18n.t('this_room_is_closed'))
            onRoomClosed?.(item.roomId!)
          } else {
            toastErr(`${res?.message}(${res?.code})`)
          }
        }
      })
      .catch((err) => {
        toastErr(`${err}`)
      })
      .finally(() => {
        setIsJoining(false)
      })
  }

  return (
    <div
      className="cursor-pointer overflow-hidden rounded-lg bg-[#2A2A2A] transition-transform duration-300 hover:scale-105"
      onClick={handleItemClicked}
    >
      <div className="relative">
        <img
          className="h-[200px] w-full object-cover"
          src={item.roomPic}
          alt="room background"
        />

        {/* 标签区域 */}
        <div className="absolute left-2 top-2 flex items-center gap-2">
          {item.tagType === 4 && item.tagContent && (
            <>
              <div className="inline-flex items-center justify-center gap-1 rounded bg-gradient-to-r from-yellow-200 to-orange-200 px-2 py-1">
                <span className="text-xs font-bold text-zinc-900">
                  Perpetual
                </span>
              </div>
              <div className="inline-flex items-center justify-center gap-1 rounded bg-white/50 px-2 py-1">
                <span className="text-xs font-bold text-zinc-900">
                  {item.tagContent}
                </span>
              </div>
            </>
          )}
          {item.tagType === 1 && item.tagContent && (
            <>
              <div className="inline-flex items-center justify-center gap-1 rounded bg-gradient-to-r from-yellow-200 to-orange-200 px-2 py-1">
                <span className="text-xs font-bold text-zinc-900">
                  Spot trade
                </span>
              </div>
              <div className="inline-flex items-center justify-center gap-1 rounded bg-white/50 px-2 py-1">
                <span className="text-xs font-bold text-zinc-900">
                  {item.tagContent}
                </span>
              </div>
            </>
          )}
        </div>

        {/* Pumpkin标签 */}
        <div className="absolute right-2 top-2">
          <div className="inline-flex items-center justify-center gap-1 rounded bg-orange-500 px-2 py-1">
            <span className="text-xs font-bold text-white">🎃 Pumpkin</span>
          </div>
        </div>

        {/* 密码锁图标 */}
        {item.isPw && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-black/50">
              <svg
                className="h-8 w-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </div>
        )}
      </div>

      {/* 房间信息 */}
      <div className="p-3">
        <div className="mb-2 line-clamp-2 text-sm font-bold text-white">
          {item.roomTitle}
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <span className="text-xs text-orange-400">🔥</span>
            <span className="text-xs text-gray-400">{item.hot ?? 0}</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-xs text-blue-400">👥</span>
            <span className="text-xs text-gray-400">{item.num ?? 0}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
