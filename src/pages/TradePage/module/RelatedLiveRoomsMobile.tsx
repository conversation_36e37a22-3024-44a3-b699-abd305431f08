import React, { useEffect, useState } from 'react'
import { Flex, Modal } from 'antd'
import {
  CHAT_ROOM_LIST_ALL,
  CHAT_ROOM_LIST_DATA,
} from '@/api/interface/CHAT_ROOM_LIST_ALL'
import { lodash } from '@/utils'
import InfiniteScroll from 'react-infinite-scroll-component'
import { RoomCard } from './RoomCard'
import i18n from '@/i18n'
import { tradeStore } from '@/pages/Trade/store/tradeStore'
import { perpsStore } from '@/store'
import { CloseOutlined } from '@ant-design/icons'
import './RelatedLiveRoomsMobile.css'

interface RelatedLiveRoomsMobileProps {
  visible: boolean
  onClose: () => void
  modeType: 'trade' | 'perp'
}

export const RelatedLiveRoomsMobile: React.FC<RelatedLiveRoomsMobileProps> = ({
  visible,
  onClose,
  modeType,
}) => {
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [roomList, setRoomList] = useState<CHAT_ROOM_LIST_DATA[]>([])

  const loadAllRooms = async () => {
    if (loading) {
      return
    }
    setLoading(true)
    try {
      const res = await CHAT_ROOM_LIST_ALL({
        page: 1,
        size: 1000,
        tagContent:
          modeType == 'trade'
            ? tradeStore.currCrypto?.symbol
            : modeType == 'perp'
              ? perpsStore.init.coin
              : '',
        // tagType: modeType == 'trade' ? 1 : modeType == 'perp' ? 4 : '',
      })
      setLoading(false)
      if (res?.code !== 200) return
      const newRecords = res.data.records
      const mergedRecords = lodash.uniqBy([...roomList, ...newRecords], 'id')
      setRoomList(mergedRecords)
      setHasMore(res.data.last !== true)
    } catch (error) {
      console.error('Failed to load rooms:', error)
      setLoading(false)
    }
  }

  useEffect(() => {
    if (visible) {
      setRoomList([]) // 重置房间列表
      loadAllRooms()
    }
  }, [visible, modeType])

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      closable={false}
      width="100%"
      className="related-live-mobile-modal"
      style={{
        top: 0,
        padding: 0,
        maxWidth: '100vw',
        height: '100vh',
        margin: 0,
      }}
      bodyStyle={{
        padding: 0,
        height: '100vh',
        backgroundColor: '#1A1B1E',
        borderRadius: 0,
      }}
      maskStyle={{
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
      }}
      styles={{
        content: {
          padding: 0,
          borderRadius: 0,
          backgroundColor: '#1A1B1E',
        },
      }}
    >
      <div className="flex h-full w-full flex-col bg-[#1A1B1E]">
        {/* 头部 */}
        <div className="flex items-center justify-between bg-[#1A1B1E] px-4 py-4">
          <div className="text-lg font-bold text-white">Related Live</div>
          <button
            onClick={onClose}
            className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2A2A2A] hover:bg-[#3A3A3A]"
          >
            <CloseOutlined className="text-white" />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden">
          <div
            id="related-live-mobile-scrollable-div"
            className="h-full w-full overflow-y-auto overflow-x-hidden p-4"
          >
            {roomList.length === 0 && !loading ? (
              <Flex className="h-full items-center justify-center">
                <div className="font-lexend text-gray-400">暂无相关直播</div>
              </Flex>
            ) : (
              <InfiniteScroll
                dataLength={roomList.length}
                next={loadAllRooms}
                hasMore={hasMore}
                loader={
                  <Flex className="items-center justify-center">
                    <div className="pt-2 font-lexend text-gray-400">
                      {i18n.t('loading')}
                    </div>
                  </Flex>
                }
                scrollableTarget="related-live-mobile-scrollable-div"
              >
                <div className="mx-auto grid w-[200px] grid-cols-1 gap-4">
                  {roomList.map((item) => (
                    <RoomCard
                      key={item.id}
                      item={item}
                      type="HOME"
                      className="h-[228px] bg-[#2A2A2A]"
                      onRoomClosed={(roomId) => {
                        setRoomList((prev) =>
                          prev.filter((room) => room.roomId !== roomId),
                        )
                      }}
                    />
                  ))}
                </div>
              </InfiniteScroll>
            )}
          </div>
        </div>
      </div>
    </Modal>
  )
}
