import React, { useEffect, useRef, useState, useCallback, memo } from 'react'
import { observer } from 'mobx-react-lite'
import { chatroomManager, RoomRole } from '@/managers/chatroomManager'
import { Flex, Typography } from 'antd'
import {
  IconChatClearMsg,
  IconChatSendMsg,
  SvgIconTwitterXRoom,
} from '@/imgs/icons'
import { useStore } from '../ChatRoomComponent'
import PKAvatar from '@/components/Customize/PKAvatar'
import styled from 'styled-components'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { ErrorCode } from '@rongcloud/engine'
import i18n from '@/i18n'
import CombinedUserPopover from '@/pages/ChatRoom/module/CombinedUserPopover'
import { kChatRoomMessage } from '@/defines/defines'
import AutoSizer from 'react-virtualized-auto-sizer'
import { VariableSizeList, ListChildComponentProps } from 'react-window'
const VariableSizeListWrapper =
  VariableSizeList as unknown as React.ComponentType<any>
interface ChatMessage {
  avatar: string
  nickname: string
  message: string
  msgType?: string
  oriModel: any
  senderUserId?: string
  twitterInfo?: {
    screenName: string
  }
}
interface RowData {
  messages: ChatMessage[]
  setRowHeight: (index: number, height: number) => void
  defaultItemHeight: number
}
const StyledInput = styled.input`
  outline: none;
  box-shadow: none;
  border: none;
  background: transparent;
  font-family: Lexend, sans-serif;
  &::placeholder {
    font-family: Lexend, sans-serif;
  }
`
const { Text } = Typography
interface TextMessageProps {
  message: ChatMessage
}
const TextMessage: React.FC<TextMessageProps> = memo(({ message }) => {
  const buildTwitterStatus = (twitterInfo?: { screenName: string }) => {
    return twitterInfo ? (
      <a
        href={`https://x.com/${twitterInfo.screenName}`}
        target="_blank"
        rel="noopener noreferrer"
      >
        <SvgIconTwitterXRoom className="ml-2 h-4 w-4 cursor-pointer" />
      </a>
    ) : null
  }
  return (
    <Flex vertical className="text-white">
      <CombinedUserPopover userId={message.senderUserId || ''}>
        <Flex className="cursor-pointer items-center">
          <PKAvatar src={message.avatar} className="size-8" />
          <Flex className="ml-2 font-lexend font-medium text-[#9293A0]">
            {message.nickname}
            {buildTwitterStatus(message.twitterInfo)}
          </Flex>
        </Flex>
      </CombinedUserPopover>
      <Flex className="ml-12 mr-2 break-words bg-[#212329] px-2.5 py-3">
        <Text className="font-lexend text-white" ellipsis={false}>
          {message.message}
        </Text>
      </Flex>
    </Flex>
  )
})
TextMessage.displayName = 'TextMessage'

interface TipMessageProps {
  message: ChatMessage
}
const TipMessage: React.FC<TipMessageProps> = memo(({ message }) => {
  const buildTwitterStatus = (twitterInfo?: { screenName: string }) => {
    return twitterInfo ? (
      <a
        href={`https://x.com/${twitterInfo.screenName}`}
        target="_blank"
        rel="noopener noreferrer"
      >
        <SvgIconTwitterXRoom className="ml-2 h-4 w-4 cursor-pointer" />
      </a>
    ) : null
  }
  const isJoinMessage =
    message?.oriModel?.content?.msgFields?.tips === 'chat_room_join'
  const userName =
    (message?.oriModel?.content?.user?.name as string) || 'unknown'
  const messageContent = isJoinMessage ? (
    <Flex>
      <Flex
        className="items-center"
        style={{
          color: '#FFB800',
          fontSize: '14px',
          fontWeight: 500,
          marginRight: '8px',
          cursor: 'pointer',
        }}
      >
        <CombinedUserPopover
          userId={message.senderUserId || message?.oriModel?.content?.user?.id}
        >
          {userName}
        </CombinedUserPopover>
        {buildTwitterStatus(message.twitterInfo)}
      </Flex>
      <span style={{ color: '#FFFFFF', fontSize: '14px', fontWeight: 500 }}>
        {i18n.t('welcome_to_join')}
      </span>
    </Flex>
  ) : (
    i18n.t('welcome_to_pumpkin')
  )
  return (
    <div className="text-white">
      <div className="font-lexend">{messageContent}</div>
    </div>
  )
})
TipMessage.displayName = 'TipMessage'

const Row: React.FC<ListChildComponentProps<RowData>> = memo(
  ({ index, style, data }) => {
    const { messages, setRowHeight, defaultItemHeight } = data
    const message = messages[index]
    const rowRef = useRef<HTMLDivElement>(null)
    useEffect(() => {
      if (rowRef.current) {
        const height = rowRef.current.getBoundingClientRect().height
        if (height && height !== defaultItemHeight) {
          setRowHeight(index, height)
        }
      }
    }, [index, message])
    return (
      <div style={style}>
        <div ref={rowRef} style={{ paddingBottom: '10px' }}>
          {message.msgType === 'RC:IWStatusMsg' ? (
            <TipMessage message={message} />
          ) : (
            <TextMessage message={message} />
          )}
        </div>
      </div>
    )
  },
)
Row.displayName = 'Row'

const ChatRoom: React.FC<{ contentClassName?: string }> = observer(
  ({ contentClassName }) => {
    const { toastSuc } = useToastMessage()
    const store = useStore()
    const [message, setMessage] = useState<string>(
      () => localStorage.getItem(kChatRoomMessage) || '',
    )
    const [isSending, setIsSending] = useState<boolean>(false)
    useEffect(() => {
      localStorage.setItem(kChatRoomMessage, message)
    }, [message])
    const handleClickSendTextMsg = useCallback(async () => {
      if (message.trim() && !isSending) {
        try {
          setIsSending(true)
          await chatroomManager.sendTextMsgInRoom(
            store.roomId.toString(),
            message,
          )
          setMessage('')
          localStorage.removeItem(kChatRoomMessage)
        } catch (error) {
          console.error('发送消息失败:', error)
        } finally {
          setIsSending(false)
        }
      }
    }, [message, store.roomId, isSending])
    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter') {
        handleClickSendTextMsg()
      }
    }
    // 动态高度测量
    const listRef = useRef<VariableSizeList<RowData>>(null)
    const defaultItemHeight = 60 // 默认预估高度
    const rowHeightsRef = useRef<{ [index: number]: number }>({})
    const setRowHeight = useCallback((index: number, size: number) => {
      if (rowHeightsRef.current[index] !== size) {
        rowHeightsRef.current = {
          ...rowHeightsRef.current,
          [index]: size,
        }
        if (listRef.current) {
          listRef.current.resetAfterIndex(index)
        }
      }
    }, [])
    const getItemSize = useCallback(
      (index: number) => rowHeightsRef.current[index] || defaultItemHeight,
      [defaultItemHeight],
    )
    const scrollToBottom = () => {
      if (listRef.current && store.messages.length > 0) {
        listRef.current.scrollToItem(store.messages.length - 1, 'end')
      }
    }
    useEffect(() => {
      scrollToBottom()
    }, [store.messages.length])
    const inputBarRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
      if (!window.visualViewport || !inputBarRef.current) return

      const handler = () => {
        const height = window.visualViewport?.height ?? 0
        const innerHeight = window.innerHeight
        const diff = innerHeight - height
        const isAndroid = /android/i.test(navigator.userAgent)
        if (isAndroid && diff > 0) {
          inputBarRef.current.style.transform = `translateY(-${diff}px)`
        } else {
          inputBarRef.current.style.transform = ''
        }
      }

      window.visualViewport.addEventListener('resize', handler)
      window.visualViewport.addEventListener('scroll', handler)
      return () => {
        window.visualViewport?.removeEventListener('resize', handler)
        window.visualViewport?.removeEventListener('scroll', handler)
      }
    }, [])
    return (
      <Flex
        vertical
        className={`flex h-full w-full flex-col border border-[#313131] bg-[#131313] p-2.5 ${
          contentClassName || ''
        }`}
      >
        <div
          className="flex-grow bg-[#131313] no-scroll md:bg-inherit"
          style={{ height: '100%', width: '100%' }}
        >
          <AutoSizer>
            {({ height, width }: { height: number; width: number }) => (
              <VariableSizeListWrapper
                className="bg-[#131313] no-scroll md:bg-inherit"
                height={height}
                width={width}
                itemCount={store.messages.length}
                itemSize={getItemSize}
                ref={listRef}
                itemData={{
                  messages: store.messages,
                  setRowHeight,
                  defaultItemHeight,
                }}
              >
                {Row}
              </VariableSizeListWrapper>
            )}
          </AutoSizer>
        </div>
        <Flex
          className="items-cente h-[48px] w-full bg-[#131313] md:mb-2 md:bg-inherit"
          ref={inputBarRef}
        >
          {/* {store.roomDetial.roomRole !== RoomRole.Civilian ? (
            <IconChatClearMsg
              className="mr-2 h-[48px] w-[48px] flex-shrink-0 cursor-pointer"
              onClick={handleClearMessages}
            />
          ) : null} */}
          <Flex className="h-[48px] flex-1 items-center rounded-[24px] border border-[#313131] px-3">
            <StyledInput
              className="mx-2 h-[46px] flex-1"
              maxLength={200}
              type="text"
              value={message}
              placeholder={i18n.t('enter')}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
            />
            <IconChatSendMsg
              className="h-[20px] w-[20px] cursor-pointer"
              onClick={handleClickSendTextMsg}
            />
          </Flex>
        </Flex>
      </Flex>
    )
  },
)
export default ChatRoom
