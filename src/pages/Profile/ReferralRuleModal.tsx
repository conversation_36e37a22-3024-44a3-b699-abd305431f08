import styled from 'styled-components'
import { useEffect, useState } from 'react'
import { SHARE_SETTING } from '@/api/interface/SHARE_SETTING'
import i18n from '@/i18n'
import { useMediaQuery } from 'react-responsive'
import { Modal } from '@/components/Customize/Modal'
import { ButtonBlack } from '@/components/Customize/Button'
import { useDevice } from '@/hooks/useDevice'

interface ReferralRuleModalProps {
  open: boolean
  onClose: () => void
}

const StyledModal = styled(Modal)`
  .ant-modal-title {
    text-align: center;
    font-size: 18px;
    font-weight: 700;
    line-height: 24px;
  }

  .ant-modal-content {
    padding: 40px;
    width: 500px;
  }

  p {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #5f606d;
  }

  .table-container {
    border-collapse: collapse;
  }

  .table-header {
    height: 40px;
    font-size: 12px;
    font-weight: 700;
    line-height: 16px;
    border: 1px solid #e5e5e5;
    border-bottom: none;
    background: #f2f3f7;
    display: grid;
    grid-template-columns: 100px 1fr 1fr;
    align-items: center;
  }

  .table-row {
    padding: 8px;
    display: grid;
    grid-template-columns: 100px 1fr 1fr;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    border: 1px solid #e5e5e5;
    border-top: none;
    background: #fff;
    font-size: 12px;
  }
`

// Mobile specific styled component using a full-screen drawer approach
const MobileDrawer = styled.div<{ open: boolean }>`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: white;
  border-radius: 16px 16px 0 0;
  padding: 20px;
  z-index: 1000;
  max-height: 80vh;
  overflow-y: auto;
  transition: transform 0.3s ease-out;
  transform: ${(props) => (props.open ? 'translateY(0)' : 'translateY(100%)')};

  .modal-title {
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    margin-bottom: 16px;
  }

  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    color: #5f606d;
  }

  .table-container {
    border-collapse: collapse;
  }

  .table-header {
    height: 40px;
    font-size: 10px;
    font-weight: 700;
    line-height: 16px;
    border: 1px solid #e5e5e5;
    border-bottom: none;
    background: #f2f3f7;
    display: grid;
    grid-template-columns: 80px 1fr 1fr;
    align-items: center;
  }

  .table-row {
    padding: 6px;
    display: grid;
    grid-template-columns: 80px 1fr 1fr;
    align-items: center;
    gap: 5px;
    align-self: stretch;
    border: 1px solid #e5e5e5;
    border-top: none;
    background: #fff;
    font-size: 10px;
  }
`

const Overlay = styled.div<{ open: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 999;
  opacity: ${(props) => (props.open ? 1 : 0)};
  visibility: ${(props) => (props.open ? 'visible' : 'hidden')};
  transition: opacity 0.3s ease-out;
`

export const ReferralRuleModal = ({
  open,
  onClose,
}: ReferralRuleModalProps) => {
  const [settings, setSettings] = useState<{
    roomShareRate: number | null
    superiorShareRate: number | null
    contractSuperiorShareRate: number | null
    contractRoomShareRate: number | null
  }>({
    roomShareRate: null,
    superiorShareRate: null,
    contractSuperiorShareRate: null,
    contractRoomShareRate: null,
  })
  const { isMobile } = useDevice()

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const res = await SHARE_SETTING()
        setSettings({
          roomShareRate: res.data.roomShareRate,
          superiorShareRate: res.data.superiorShareRate,
          contractSuperiorShareRate: res.data.contractSuperiorShareRate,
          contractRoomShareRate: res.data.contractRoomShareRate,
        })
      } catch (error) {
        console.error('获取设置失败:', error)
      }
    }

    fetchSettings()
  }, [open])

  // Modal content to be shared between mobile and desktop
  const ModalContent = () => (
    <div className="space-y-3 py-2 md:space-y-4 md:py-4">
      <p>{i18n.t('invite_commission_rule_1')}</p>
      <p>{i18n.t('invite_commission_rule_2')}</p>
      <p>{i18n.t('invite_commission_rule_3')}</p>
      <div className="rounded bg-[#F8F9FB]">
        <div className="table-container">
          <div className="table-header">
            <div></div>
            <div>{i18n.t('spot_trading')}</div>
            <div>{i18n.t('perp_trading')}</div>
          </div>
          <div className="table-row">
            <div>{i18n.t('inviter_rule')}</div>
            <div>
              {' '}
              {settings.superiorShareRate
                ? (settings.superiorShareRate * 100).toFixed(0)
                : '--'}
              %
            </div>
            <div>
              {settings.contractSuperiorShareRate
                ? (settings.contractSuperiorShareRate * 100).toFixed(0)
                : '--'}
              %
            </div>
          </div>
          <div className="table-row">
            <div>{i18n.t('host_rule')}</div>
            <div>
              {settings.roomShareRate
                ? (settings.roomShareRate * 100).toFixed(0)
                : '--'}
              %
            </div>
            <div>
              {settings.contractRoomShareRate
                ? (settings.contractRoomShareRate * 100).toFixed(0)
                : '--'}
              %
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  if (isMobile) {
    return (
      <>
        <Overlay open={open} onClick={onClose} />
        <MobileDrawer open={open}>
          <div className="modal-title">{i18n.t('invite_commission_rule')}</div>
          <ModalContent />
          <ButtonBlack className="mt-4 h-9 w-full rounded-lg" onClick={onClose}>
            <div>{i18n.t('i_know')}</div>
          </ButtonBlack>
        </MobileDrawer>
      </>
    )
  }

  return (
    <StyledModal
      title={i18n.t('invite_commission_rule')}
      open={open}
      onCancel={onClose}
      width={504}
      centered
      footer={[
        <ButtonBlack
          key="submit"
          className="w-full rounded-lg py-3 text-sm"
          onClick={onClose}
        >
          <div>{i18n.t('i_know')}</div>
        </ButtonBlack>,
      ]}
    >
      <ModalContent />
    </StyledModal>
  )
}
