import { useEffect, useState } from 'react'
import { useRoute } from '@/hooks/useRoute'
import {
  SvgIconTwitterX,
  SvgIconTwitterXAuthed,
  IconMale,
  IconFemale,
  SvgIconProfileCopy,
  SvgIconQuestionCircle,
  SvgIconArrowRight,
  SvgIconCustomerService,
  SvgIconSettings,
  SvgIconTelegramDark,
  SvgIconMobileEdit,
  SvgIconPumpkinApp,
  SvgIconWalletEye,
  SvgIconWalletEyeSlash,
  // SvgIconTSol,
  // SvgIconTUSDT,
  SvgIconWalletCopy,
  SvgIconPerps,
  SvgIconSpot,
} from '@/imgs/icons'
import { userStore, walletStore, perpsStore } from '@/store'
import { Flex } from 'antd'
import styled from 'styled-components'
import { UR_COUNT } from '@/api/interface/UR_COUNT'
import { observer } from 'mobx-react-lite'
import { message } from 'antd'
import { ReferralRuleModal } from '@/pages/Profile/ReferralRuleModal'
import PointsImg from '../Points/images/points.png'
import {
  GET_USER_BIND_ACCOUNT,
  BindAccountType,
} from '@/api/interface/GET_USER_BIND_ACCOUNT'
import { GET_ARBITRUM_ADDRESS } from '@/api/interface/GET_ARBITRUM_ADDRESS'
import {
  GET_ALL_TOKEN_BALANCE,
  ChainId,
} from '@/api/interface/GET_ALL_TOKEN_BALANCE'
import { USERINFO } from '@/api/interface/USERINFO'
import { Hex } from 'viem'
import i18n from '@/i18n'
import { GET_MY_POINTS } from '@/api/interface/GET_MY_POINTS'
import { milliFormat } from '@/utils'

const ProfileBtn = styled.button`
  background: black;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  &:hover {
    background: black;
  }
`

declare global {
  interface Window {
    Intercom: any
  }
}

export const Component = observer(() => {
  const [pointsData, setPointsData] = useState({
    totalPoints: 0,
  })
  // const store = useStore()
  // const { logout } = usePrivy()
  const { path } = useRoute()
  const [counts, setCounts] = useState({
    likeCount: 0,
    likeMeCount: 0,
    myFriendsCount: 0,
    tips: 0,
  })
  const [showBalance, setShowBalance] = useState(true)
  const [showSeed, setShowSeed] = useState(true)
  const [spotBalance, setSpotBalance] = useState('0')
  const [perpsBalance, setPerpsBalance] = useState('0')
  const [_loadingBalance, setLoadingBalance] = useState(false)

  const Intercom = (window as any).Intercom

  const inviteLink = userStore.info.conciseUserId
    ? `${import.meta.env.VITE_APP_URL}/?cid=${userStore.info.conciseUserId || ''}`
    : '--'

  const [isRuleModalOpen, setIsRuleModalOpen] = useState(false)
  // 不再需要存储钱包地址信息，直接使用 walletStore

  useEffect(() => {
    if (!userStore.IS_LOGIN()) {
      path('/')
    }
    Intercom('boot', { hide_default_launcher: true })
  }, [])

  useEffect(() => {
    const fetchCounts = async () => {
      try {
        const response = await UR_COUNT()
        if (response.code === 200) {
          setCounts(response.data)
        }
      } catch (error) {
        console.error('获取数据失败:', error)
      }
    }

    fetchCounts()
  }, [])

  // 获取用户的Solana和EVM钱包地址，并存储到 walletStore 中供钱包页面使用
  useEffect(() => {
    const fetchWalletAddresses = async () => {
      try {
        // 获取Solana和EVM地址
        const res = await GET_USER_BIND_ACCOUNT()
        if (res.code === 200) {
          // 获取Solana和EVM地址及connector_type
          const solanaAccount = res.data.bindAccountList.find(
            (account) => account.type === BindAccountType.WALLET_SOL,
          )
          const evmAccount = res.data.bindAccountList.find(
            (account) => account.type === BindAccountType.WALLET,
          )

          // 将地址存储在 walletStore 中，供钱包页面使用
          if (solanaAccount?.address) {
            walletStore.setSolanaAddress(
              solanaAccount.address,
              solanaAccount.connector_type || '',
            )
          }
          if (evmAccount?.address) {
            walletStore.setEvmAddress(
              evmAccount.address,
              evmAccount.connector_type || '',
            )
          }
        }
      } catch (error) {
        console.error('获取钱包地址失败:', error)
      }
    }

    fetchWalletAddresses()
  }, [])

  // 获取用户的Arbitrum钱包地址，并存储到 walletStore 中供合约钱包页面使用
  useEffect(() => {
    const fetchArbitrumAddress = async () => {
      try {
        const response = await GET_ARBITRUM_ADDRESS()
        if (response.code === 200 && response.data) {
          // 将Arbitrum地址存储在 walletStore 中，供合约钱包页面使用
          walletStore.setArbitrumAddress(response.data)
        }
      } catch (error) {
        console.error('获取Arbitrum地址失败:', error)
      }
    }

    fetchArbitrumAddress()
  }, [])

  // 获取现货钱包余额（所有链的总和，不包括 Arbitrum）
  useEffect(() => {
    const fetchSpotBalance = async () => {
      try {
        setLoadingBalance(true)
        // 检查是否有地址
        const evmAddress = walletStore.evmAddress
        const solanaAddress = walletStore.solanaAddress
        if (!evmAddress && !solanaAddress) {
          setLoadingBalance(false)
          return
        }

        // 定义要查询的链网络
        const networks = ['Solana', 'Base', 'ETH', 'BSC']
        let totalSpotBalance = 0

        // 对每个链进行查询
        for (const network of networks) {
          try {
            // 确定当前链的地址
            const address =
              network.toLowerCase() === 'solana' ? solanaAddress : evmAddress
            if (!address) continue

            // 确定链 ID
            const chainId: ChainId = (() => {
              switch (network) {
                case 'ETH':
                  return 'ethereum'
                case 'BSC':
                  return 'bsc'
                case 'Base':
                  return 'base'
                case 'Solana':
                  return 'solana'
                default:
                  return 'ethereum'
              }
            })()

            // 获取该链的余额
            const res = await GET_ALL_TOKEN_BALANCE(address, chainId)
            if (res.code === 200) {
              const tokenAssets = Array.isArray(res.data)
                ? res.data[0]?.tokenAssets
                : res.data.tokens || []

              const validTokens =
                tokenAssets && Array.isArray(tokenAssets)
                  ? tokenAssets.filter(
                      (token: any) => Number(token.tokenPrice) > 0,
                    )
                  : []

              // 计算该链的总余额
              const networkBalance = validTokens.reduce(
                (sum: number, token: any) =>
                  sum + (Number(token.balance) * Number(token.tokenPrice) || 0),
                0,
              )

              // 累加到总余额
              totalSpotBalance += networkBalance
              console.log(`${network} balance:`, networkBalance.toFixed(6))
            }
          } catch (error) {
            console.error(`获取 ${network} 钱包余额失败:`, error)
          }
        }

        // 设置总余额
        setSpotBalance(totalSpotBalance.toFixed(6))
      } catch (error) {
        console.error('获取现货钱包余额失败:', error)
        setSpotBalance('0')
      } finally {
        setLoadingBalance(false)
      }
    }

    fetchSpotBalance()
  }, [walletStore.evmAddress, walletStore.solanaAddress])

  // 获取合约钱包余额
  useEffect(() => {
    const fetchPerpsBalance = async () => {
      try {
        setLoadingBalance(true)
        const arbitrumAddress = walletStore.arbitrumAddress
        if (!arbitrumAddress) {
          setLoadingBalance(false)
          return
        }

        // 确保 perpsStore.meta 已初始化
        if (Object.keys(perpsStore.meta.allData).length === 0) {
          try {
            // 不需要 await，因为 getAllCoinsInfo 不返回 Promise
            perpsStore.meta.getAllCoinsInfo()
          } catch (error) {
            console.error('初始化 perpsStore.meta 失败', error)
            setLoadingBalance(false)
            return
          }
        }

        // 将地址转换为 Hex 类型
        const userAddress = arbitrumAddress as Hex

        // 获取账户概览数据
        perpsStore.user.getAccountOverview(userAddress)

        // 由于 getAccountOverview 是异步的但不返回 Promise，我们需要添加一个延迟
        // 等待数据加载完成
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // 使用 perpsStore.user.rawBalance.accountValue 作为账户总价值
        let accountValue = 0

        // 从 perpsStore.user.rawBalance 中获取账户价值
        if (
          perpsStore.user &&
          perpsStore.user.rawBalance &&
          perpsStore.user.rawBalance.accountValue
        ) {
          accountValue = parseFloat(perpsStore.user.rawBalance.accountValue)
        }

        // 设置合约钱包余额
        setPerpsBalance(accountValue.toFixed(6))
      } catch (error) {
        console.error('获取合约钱包余额失败:', error)
        setPerpsBalance('0')
      } finally {
        setLoadingBalance(false)
      }
    }

    fetchPerpsBalance()
  }, [walletStore.arbitrumAddress])

  useEffect(() => {
    const refreshUserInfo = async () => {
      const res = await USERINFO()

      if (res.code === 200) {
        userStore.setInfo(res.data as any)
      }
    }

    refreshUserInfo()
  }, [])

  // 获取积分数据
  const fetchPointsData = async () => {
    try {
      const response = await GET_MY_POINTS()
      if (response.code === 200 && response.data) {
        setPointsData({
          totalPoints: response.data.totalGoldCoin,
        })
      }
    } catch (error) {
      console.error('获取积分数据失败:', error)
    }
  }

  useEffect(() => {
    fetchPointsData()
  }, [])

  // const handleLogout = () => {
  //   userStore.reset()
  //   chatroomManager.logout()
  //   store.leaveRoom()
  //   logout()
  //   path('/')
  // }

  return (
    <div className="absolute inset-0 overflow-y-auto bg-[#F5F5F5] md:relative">
      <Flex
        className="mb-10 space-y-4 px-4 pb-10 pt-5 md:mb-0 md:ml-20 md:mr-20 md:px-20 md:pb-5 md:pt-10"
        vertical={true}
      >
        <Flex className="h-auto w-full rounded-[8px] bg-white px-3 py-6 md:h-56 md:px-5">
          <Flex vertical className="w-full">
            <Flex className="items-start justify-between">
              <Flex className="flex-row">
                <Flex className="flex-col items-center md:block">
                  <img
                    className="size-16 cursor-pointer rounded-full object-cover md:size-28"
                    src={userStore.info?.profile?.photos[0]?.path}
                    alt="avatar"
                    onClick={() => path(`/user/${userStore.info.id}`)}
                  />
                  {userStore.info.conciseUserId && (
                    <Flex className="-mt-2 h-auto items-center md:hidden">
                      <ProfileBtn
                        className="flex h-6 items-center justify-center !rounded-full px-2 text-[10px] font-bold leading-6"
                        onClick={() => path('/profile/edit')}
                      >
                        {i18n.t('edit')}
                        <SvgIconMobileEdit className="ml-1 h-3 w-3" />
                      </ProfileBtn>
                    </Flex>
                  )}
                </Flex>
                <Flex vertical className="ml-3 md:ml-4">
                  <Flex className="items-center">
                    <span className="mb-1 max-w-[130px] truncate text-lg font-bold text-[#1A1B1E] md:mb-2 md:h-8 md:max-w-[250px] md:text-2xl">
                      {userStore.info?.nickname || '--'}
                    </span>
                    {userStore.info?.twitterInfo?.id ? (
                      <SvgIconTwitterXAuthed
                        className="ml-1 h-4 w-4 flex-shrink-0 cursor-pointer md:ml-2 md:h-5 md:w-5"
                        onClick={() => {
                          if (userStore.info.conciseUserId) {
                            path('/profile/twitter-unbind')
                          }
                        }}
                      />
                    ) : (
                      <SvgIconTwitterX
                        className="ml-1 h-4 w-4 flex-shrink-0 cursor-pointer md:ml-2 md:h-5 md:w-5"
                        onClick={() => {
                          if (userStore.info.conciseUserId) {
                            path('/profile/twitter-bind')
                          }
                        }}
                      />
                    )}
                  </Flex>
                  <Flex className="items-center">
                    <span className="max-w-[130px] truncate text-xs font-normal leading-5 text-[#5F606D] md:max-w-[250px] md:text-base md:leading-6">
                      ID:{userStore.info.conciseUserId || '--'}
                    </span>
                    {userStore.info.conciseUserId && (
                      <SvgIconWalletCopy
                        className="ml-1 size-3 flex-shrink-0 cursor-pointer md:size-4"
                        onClick={() => {
                          try {
                            navigator.clipboard.writeText(
                              userStore.info.conciseUserId,
                            )
                            message.success(i18n.t('copy_success'))
                          } catch (error) {
                            message.error(i18n.t('copy_failed'))
                            console.error('复制失败:', error)
                          }
                        }}
                      />
                    )}
                  </Flex>
                  {(userStore.info?.profile?.gender ||
                    userStore.info?.profile?.birthday) && (
                    <span className="mt-1 h-5 w-fit rounded border border-[#DADBE1] px-1 text-xs font-semibold leading-5 text-[#1A1B1E] md:mt-2 md:h-6 md:text-sm md:leading-6">
                      {userStore.info?.profile?.gender === 1 ? (
                        <IconMale className="mr-0.5 inline-block h-3 w-3 md:h-[14px] md:w-[14px]" />
                      ) : userStore.info?.profile?.gender === 2 ? (
                        <IconFemale className="mr-0.5 inline-block h-3 w-3 md:h-[14px] md:w-[14px]" />
                      ) : null}
                      {userStore.info?.profile?.birthday
                        ? new Date().getFullYear() -
                          new Date(
                            userStore.info.profile.birthday,
                          ).getFullYear()
                        : ''}
                    </span>
                  )}
                </Flex>
              </Flex>
              {userStore.info.conciseUserId && (
                <Flex className="hidden h-auto items-center md:mt-0 md:flex md:h-28">
                  <ProfileBtn
                    className="flex h-[40px] items-center justify-center px-6 text-[14px] font-[400]"
                    onClick={() => path('/profile/edit')}
                  >
                    {i18n.t('edit_profile_btn')}
                  </ProfileBtn>
                </Flex>
              )}
            </Flex>

            <Flex className="mt-4 border-t border-gray-100 pt-3 md:pt-4">
              <Flex
                className="mr-4 cursor-pointer items-center gap-1 md:mr-9"
                onClick={() => path('/profile/friends')}
              >
                <span className="text-sm font-bold md:text-lg">
                  {counts.myFriendsCount}
                </span>
                <span className="text-xs font-normal text-[#5F606D] md:text-base md:leading-6">
                  {i18n.t('friends')}
                </span>
              </Flex>
              <Flex
                className="mr-4 cursor-pointer items-center gap-1 md:mr-9"
                onClick={() => {
                  if (counts.likeCount > 0) {
                    path('/profile/followings')
                  }
                }}
              >
                <span className="text-sm font-bold md:text-lg">
                  {counts.likeCount}
                </span>
                <span className="text-xs font-normal text-[#5F606D] md:text-base md:leading-6">
                  {i18n.t('following')}
                </span>
              </Flex>
              <Flex
                className="cursor-pointer items-center gap-1"
                onClick={() => {
                  if (counts.likeMeCount > 0) {
                    path('/profile/followers')
                  }
                }}
              >
                <span className="text-sm font-bold md:text-lg">
                  {counts.likeMeCount}
                </span>
                <span className="text-xs font-normal text-[#5F606D] md:text-base md:leading-6">
                  {i18n.t('followers')}
                </span>
              </Flex>
            </Flex>
          </Flex>
        </Flex>

        <Flex
          className="w-full cursor-pointer rounded-[8px] bg-white p-3 md:p-5"
          vertical={true}
          onClick={() => {
            if (userStore.info.conciseUserId) {
              path('/wallet')
            }
          }}
        >
          <Flex className="items-center justify-between">
            <span className="text-sm font-bold text-[#1A1B1E] md:text-lg">
              {i18n.t('wallet_crypto_assets')}{' '}
              {showBalance ? (
                <SvgIconWalletEye
                  className="inline-block size-4 cursor-pointer md:size-5"
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowBalance(false)
                  }}
                />
              ) : (
                <SvgIconWalletEyeSlash
                  className="inline-block size-4 cursor-pointer md:size-5"
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowBalance(true)
                  }}
                />
              )}
            </span>
          </Flex>
          <Flex className="mt-3 flex-1 flex-col gap-2 md:mt-4 md:gap-3">
            <Flex
              className="items-center justify-between rounded-lg p-2"
              onClick={(e) => {
                e.stopPropagation()
                if (userStore.info.conciseUserId) {
                  path('/wallet?tab=spot')
                }
              }}
            >
              <Flex className="items-center gap-2">
                <div className="flex size-8 items-center justify-center">
                  <SvgIconSpot className="size-8" />
                </div>
                <span className="text-[14px] font-medium text-[#1A1B1E] md:text-sm">
                  {i18n.t('wallet_tab_spot')}
                </span>
              </Flex>
              <Flex className="items-center gap-2">
                <span className="text-[14px] font-bold text-[#1A1B1E] md:text-sm">
                  {showBalance ? `$${spotBalance}` : '****'}
                </span>
                <SvgIconArrowRight className="size-4 md:size-5" />
              </Flex>
            </Flex>
            <Flex
              className="items-center justify-between rounded-lg p-2"
              onClick={(e) => {
                e.stopPropagation()
                if (userStore.info.conciseUserId) {
                  path('/wallet?tab=perps')
                }
              }}
            >
              <Flex className="items-center gap-2">
                <div className="flex size-8 items-center justify-center">
                  <SvgIconPerps className="size-8" />
                </div>
                <span className="text-[14px] font-medium text-[#1A1B1E] md:text-sm">
                  {i18n.t('wallet_tab_perp')}
                </span>
              </Flex>
              <Flex className="items-center gap-2">
                <span className="text-[14px] font-bold text-[#1A1B1E] md:text-sm">
                  {showBalance ? `$${perpsBalance}` : '****'}
                </span>
                <SvgIconArrowRight className="size-4 md:size-5" />
              </Flex>
            </Flex>
          </Flex>
        </Flex>

        <Flex
          className="w-full cursor-pointer rounded-[8px] bg-white p-3 md:p-5"
          vertical={true}
          onClick={() => {
            if (userStore.info.conciseUserId) {
              path('/points')
            }
          }}
        >
          <Flex className="items-center justify-between">
            <span className="text-sm font-bold text-[#1A1B1E] md:text-lg">
              Platform Assets{' '}
              {showSeed ? (
                <SvgIconWalletEye
                  className="inline-block size-4 cursor-pointer md:size-5"
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowSeed(false)
                  }}
                />
              ) : (
                <SvgIconWalletEyeSlash
                  className="inline-block size-4 cursor-pointer md:size-5"
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowSeed(true)
                  }}
                />
              )}
            </span>
          </Flex>
          <Flex className="mt-3 flex-1 flex-col gap-2 md:mt-4 md:gap-3">
            <Flex
              className="items-center justify-between rounded-lg p-2"
              onClick={(e) => {
                e.stopPropagation()
                if (userStore.info.conciseUserId) {
                  path('/points')
                }
              }}
            >
              <Flex className="items-center gap-2">
                <div className="flex size-8 items-center justify-center">
                  <img src={PointsImg} alt="Points" className="mr-1 size-8" />
                </div>
                <span className="text-[14px] font-medium text-[#1A1B1E] md:text-sm">
                  Pumpkin Seed
                </span>
              </Flex>
              <Flex className="items-center gap-2">
                <span className="text-[14px] font-bold text-[#1A1B1E] md:text-sm">
                  {showSeed ? `${pointsData.totalPoints}` : '****'}
                </span>
                <SvgIconArrowRight className="size-4 md:size-5" />
              </Flex>
            </Flex>
          </Flex>
        </Flex>

        <Flex
          className="h-auto w-full cursor-pointer rounded-[8px] bg-white p-3 md:h-36 md:p-5"
          vertical={true}
          onClick={() => {
            if (userStore.info.conciseUserId) {
              path('/profile/referral')
            }
          }}
        >
          <Flex className="items-center justify-between">
            <Flex className="items-center">
              <span className="text-sm font-bold text-[#1A1B1E] md:text-lg">
                {i18n.t('referral_commission')}
              </span>
              <span
                className="ml-1 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation()
                  setIsRuleModalOpen(true)
                }}
              >
                <SvgIconQuestionCircle className="size-4 md:size-5" />
              </span>
            </Flex>
            <SvgIconArrowRight className="size-4 md:size-5" />
          </Flex>
          <Flex className="mt-3 flex-col items-center justify-between md:mt-5 md:flex-row">
            <Flex className="mb-2 items-center md:mb-0">
              <span className="hidden text-sm text-[#1A1B1E] md:inline md:text-base">
                {i18n.t('invite_link')}
              </span>
              <span className="ml-0 max-w-full truncate text-xs text-[#5F606D] md:ml-2 md:max-w-none md:text-base">
                {inviteLink}
              </span>
            </Flex>
            <Flex
              className="cursor-pointer rounded-[4px] bg-black px-3 py-1 text-[12px] font-medium text-white md:px-4 md:py-2 md:text-sm"
              onClick={(e) => {
                e.stopPropagation()
                if (!inviteLink) return
                try {
                  navigator.clipboard.writeText(inviteLink)
                  message.success(i18n.t('copy_success'))
                } catch (error) {
                  message.error(i18n.t('copy_failed'))
                  console.error('copy failed:', error)
                }
              }}
            >
              <SvgIconProfileCopy className="mr-1 inline-block size-3 translate-y-[3px] md:size-4" />
              {i18n.t('copy')}
            </Flex>
          </Flex>
        </Flex>

        {/* 移动端显示More部分 */}
        <Flex className="mt-4 w-full rounded-[8px] border border-[#E5E5E5] bg-white p-4 md:hidden">
          <Flex vertical className="w-full">
            <span className="mb-4 text-[18px] text-lg font-bold font-normal leading-6 text-[#1A1B1E]">
              {i18n.t('profile_more_app')}
            </span>
            <Flex
              className="cursor-pointer items-center justify-between"
              onClick={() => {
                path('/mguide')
              }}
            >
              <Flex className="items-center">
                <div className="flex items-center justify-center">
                  <SvgIconPumpkinApp className="mr-3 size-7" />
                </div>
                <span className="text-sm font-medium leading-5 text-[#1A1B1E]">
                  {i18n.t('download_pumpkin_app')}
                </span>
              </Flex>
              <SvgIconArrowRight className="size-4" />
            </Flex>
          </Flex>
        </Flex>

        <Flex className="h-auto flex-row items-center justify-between gap-2 pl-2 pt-2 md:h-[40px] md:gap-4 md:pt-3">
          <Flex className="items-center gap-3 md:gap-4">
            <button
              className="flex items-center justify-center rounded-full"
              onClick={() =>
                window.open('https://t.me/pumpkinglobal_group', '_blank')
              }
            >
              <SvgIconTelegramDark className="size-5 md:size-6" />
            </button>
            <button
              className="flex items-center justify-center rounded-full"
              onClick={() =>
                window.open('https://x.com/Pumpkin_global', '_blank')
              }
            >
              <SvgIconTwitterXAuthed className="size-5 md:size-6" />
            </button>
          </Flex>
          {userStore.info.conciseUserId && (
            <Flex className="flex-row items-center gap-2 md:gap-4">
              <button
                className="flex h-[36px] min-w-[100px] items-center justify-center rounded-lg border border-[#E5E5E5] bg-white px-2 text-[12px] font-normal text-[#333] md:h-[40px] md:min-w-[120px] md:text-[14px]"
                onClick={() => {
                  // 跳转到客服页面
                  if (typeof window !== 'undefined' && window.Intercom) {
                    window.Intercom('showMessages')
                  }
                }}
              >
                <SvgIconCustomerService className="mr-1 h-4 w-4 md:h-5 md:w-5" />
                <div>{i18n.t('customer_service')}</div>
              </button>
              <button
                className="flex h-[36px] min-w-[100px] items-center justify-center rounded-lg border border-[#E5E5E5] bg-white px-2 text-[12px] font-normal text-[#333] md:h-[40px] md:min-w-[120px] md:text-[14px]"
                onClick={() => {
                  path('/settings')
                }}
              >
                <SvgIconSettings className="mr-1 h-4 w-4 md:mr-2 md:h-5 md:w-5" />
                <div>{i18n.t('profile_settings')}</div>
              </button>
            </Flex>
          )}
        </Flex>
        <ReferralRuleModal
          open={isRuleModalOpen}
          onClose={() => setIsRuleModalOpen(false)}
        />
      </Flex>
    </div>
  )
})

Component.displayName = 'ProfilePage'
export default Component
