import { useEffect, useState } from 'react'
import { Flex, message, Tooltip } from 'antd'
import { GET_MY_POINTS } from '@/api/interface/GET_MY_POINTS'
import { GET_POINTS_WAY } from '@/api/interface/GET_POINTS_WAY'
import {
  SvgIconQuestionCircleLight,
  SvgIconPointsInviteLink,
  SvgIconPointsLimit,
} from '@/imgs/icons'
import i18n from '@/i18n'
import PointsImg from './images/points.png'
import BgImg from './images/bg.png'
import Card3Img from './images/card3.png'
import Card4Img from './images/card4.png'
import { userStore } from '@/store'
import { useRoute } from '@/hooks/useRoute'
import { milliFormat } from '@/utils'

export function Component() {
  const [pointsData, setPointsData] = useState({
    totalPoints: 0,
    livePoints: 0,
    tradePoints: 0,
    volumePoints: 0,
    invitePoints: 0,
  })
  const { path } = useRoute()

  const [pointsWayData, setPointsWayData] = useState({
    liveReward: { reachStandardValue: 0, awardValue: 0, limitValue: 0 },
    tradeReward: { reachStandardValue: 0, awardValue: 0, limitValue: 0 },
    volumeReward: { reachStandardValue: 0, awardValue: 0, limitValue: 0 },
    inviteReward: { reachStandardValue: 0, awardValue: 0, limitValue: 0 },
  })

  useEffect(() => {
    fetchPointsData()
    fetchPointsWayData()
  }, [])

  const inviteLink = userStore.info.conciseUserId
    ? `${import.meta.env.VITE_APP_URL}/?cid=${userStore.info.conciseUserId || ''}`
    : '--'

  useEffect(() => {
    if (!userStore.IS_LOGIN()) {
      path('/')
    }
  }, [])

  const fetchPointsData = async () => {
    try {
      const response = await GET_MY_POINTS()
      if (response.code === 200 && response.data) {
        setPointsData({
          totalPoints: response.data.totalGoldCoin,
          livePoints: response.data.liveTime,
          tradePoints: response.data.roomBringTrading,
          volumePoints: response.data.myTrading,
          invitePoints: response.data.inviterTrading,
        })
      }
    } catch (error) {
      console.error('获取积分数据失败:', error)
    }
  }

  const fetchPointsWayData = async () => {
    try {
      const response = await GET_POINTS_WAY()
      if (response.code === 200 && response.data) {
        const newPointsWayData = { ...pointsWayData }
        response.data.forEach((data) => {
          switch (data.type) {
            case 'LIVE_TIME':
              newPointsWayData.liveReward = {
                reachStandardValue: data.reachStandardValue,
                awardValue: data.awardValue,
                limitValue: data.limitValue,
              }
              break
            case 'ROOM_TRADING':
              newPointsWayData.tradeReward = {
                reachStandardValue: data.reachStandardValue,
                awardValue: data.awardValue,
                limitValue: data.limitValue,
              }
              break
            case 'MY_TRADING':
              newPointsWayData.volumeReward = {
                reachStandardValue: data.reachStandardValue,
                awardValue: data.awardValue,
                limitValue: data.limitValue,
              }
              break
            case 'INVITER_TRADING':
              newPointsWayData.inviteReward = {
                reachStandardValue: data.reachStandardValue,
                awardValue: data.awardValue,
                limitValue: data.limitValue,
              }
              break
            default:
              console.warn(`未知的积分方式类型: ${data.type}`)
          }
        })
        setPointsWayData(newPointsWayData)
      }
    } catch (error) {
      console.error('获取积分方式数据失败:', error)
    }
  }

  return (
    <Flex
      className="h-screen space-y-4 overflow-y-auto px-0 pb-36 md:px-8 md:pb-0 md:pt-10"
      vertical={true}
    >
      <Flex className="w-full rounded-[8px] bg-white px-4 py-4 md:px-5 md:py-6">
        <Flex vertical className="w-full">
          <h2 className="mb-3 font-['Lexend'] text-[20px] font-bold leading-normal tracking-[-0.5px] text-[#1A1B1E] md:mb-4 md:text-[24px]">
            {i18n.t('points_ rewards_title')}
          </h2>
          {/* 总积分显示区域 */}
          <div
            className="relative h-[220px] w-full rounded-lg p-4 md:h-[216px] md:p-10"
            style={{
              backgroundImage: `url(${BgImg})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            <Tooltip
              title={i18n.t('points_exchange_info')}
              overlayInnerStyle={{
                backgroundColor: '#ffffff',
                color: '#000000',
                fontWeight: 'bold',
                borderRadius: '8px',
                padding: '10px',
              }}
              placement="bottomRight"
              color="#ffffff"
            >
              <SvgIconQuestionCircleLight className="absolute right-4 top-4 size-5 cursor-pointer text-white" />
            </Tooltip>
            <div className="mb-6 flex items-center justify-center md:mb-8">
              <img
                src={PointsImg}
                alt="Points"
                className="mr-2 size-6 md:size-[30px]"
              />
              <span className="mr-2 text-[32px] font-bold leading-normal text-[#F9FAFB] md:text-[30px]">
                {milliFormat(pointsData.totalPoints)}
              </span>
            </div>

            <div className="mx-auto grid w-full grid-cols-2 gap-y-4 md:w-[800px] md:grid-cols-4 md:gap-2 md:gap-4 md:gap-y-6">
              <div className="flex flex-col items-center">
                <div className="mb-1 text-[16px] font-bold text-white md:mb-2 md:text-[20px]">
                  {milliFormat(pointsData.livePoints)}
                </div>
                <div className="text-center text-[10px] text-[#9293A0] md:text-[14px]">
                  {i18n.t('points_live')}
                </div>
              </div>
              <div className="flex flex-col items-center">
                <div className="mb-1 text-[16px] font-bold text-white md:mb-2 md:text-[20px]">
                  {milliFormat(pointsData.tradePoints)}
                </div>
                <div className="text-center text-[10px] text-[#9293A0] md:text-[14px]">
                  {i18n.t('points_order_amount')}
                </div>
              </div>
              <div className="flex flex-col items-center">
                <div className="mb-1 text-[16px] font-bold text-white md:mb-2 md:text-[20px]">
                  {milliFormat(pointsData.volumePoints)}
                </div>
                <div className="text-center text-[10px] text-[#9293A0] md:text-[14px]">
                  {i18n.t('points_trading_volume')}
                </div>
              </div>
              <div className="flex flex-col items-center">
                <div className="mb-1 text-[16px] font-bold text-white md:mb-2 md:text-[20px]">
                  {milliFormat(pointsData.invitePoints)}
                </div>
                <div className="text-center text-[10px] text-[#9293A0] md:text-[14px]">
                  {i18n.t('points_invitation')}
                </div>
              </div>
            </div>
          </div>
          {/* 如何获得积分标题 */}
          <div className="mb-3 mt-4 font-['Lexend'] text-[20px] font-bold leading-normal text-[#1A1B1E] md:mb-4 md:mt-6 md:text-[24px]">
            {i18n.t('points_how_to_get_points')}
          </div>
          {/* 积分获取方式卡片 */}
          <div
            className="grid grid-cols-1 gap-3 md:grid-cols-4 md:gap-4"
            style={{ gridAutoRows: '1fr' }}
          >
            {/* 开播奖励卡片 */}
            <div
              className="flex flex-col items-center justify-center rounded-lg px-3 py-3 md:justify-start md:px-4 md:py-5"
              style={{
                backgroundImage: `url(${Card3Img})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
              }}
            >
              <div className="mb-1 mt-0 flex items-center justify-center md:mb-1 md:mt-5">
                <div className="flex items-center">
                  <img
                    src={PointsImg}
                    alt="Points"
                    className="mr-1.5 size-6 md:mr-1 md:size-8"
                  />
                  <span className="text-[22px] font-bold leading-8 text-[#1A1B1E] md:h-8 md:text-[24px]">
                    +{milliFormat(pointsWayData.liveReward.awardValue)}
                  </span>
                </div>
              </div>
              <div className="mb-1 text-center text-[15px] font-bold text-[#1A1B1E] md:mb-1 md:h-6 md:text-[16px]">
                {i18n.t('points_live_reward')}
              </div>
              <div className="mb-2 flex min-h-[40px] items-center justify-center text-center text-[12px] font-normal leading-4 text-[#9293A0] md:mb-1 md:h-12">
                {i18n.t('points_live_reward_description', {
                  hours: milliFormat(
                    pointsWayData.liveReward.reachStandardValue,
                  ),
                  points: milliFormat(pointsWayData.liveReward.awardValue),
                })}
              </div>
              <div className="flex h-4 items-center">
                <SvgIconPointsLimit className="mr-1 inline-block size-3.5 p-0.5 md:size-4" />
                <span className="text-center text-[11px] font-normal leading-normal text-[#000] md:text-[12px]">
                  {pointsWayData.liveReward.limitValue === 0
                    ? i18n.t('points_no_limit')
                    : i18n.t('points_reward_limit', {
                        limit: milliFormat(pointsWayData.liveReward.limitValue),
                      })}
                </span>
              </div>
            </div>

            {/* 带单成交卡片 */}
            <div
              className="flex flex-col items-center justify-center rounded-lg px-3 py-3 md:justify-start md:px-4 md:py-5"
              style={{
                backgroundImage: `url(${Card4Img})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
              }}
            >
              <div className="mb-1 mt-0 flex items-center justify-center md:mb-1 md:mt-5">
                <div className="flex items-center">
                  <img
                    src={PointsImg}
                    alt="Points"
                    className="mr-1.5 size-6 md:mr-1 md:size-8"
                  />
                  <span className="text-[22px] font-bold leading-8 text-[#1A1B1E] md:h-8 md:text-[24px]">
                    +{milliFormat(pointsWayData.tradeReward.awardValue)}
                  </span>
                </div>
              </div>
              <div className="mb-1 text-center text-[15px] font-bold text-[#1A1B1E] md:mb-1 md:h-6 md:text-[16px]">
                {i18n.t('points_trade_reward')}
              </div>
              <div className="mb-2 flex min-h-[40px] items-center justify-center text-center text-[12px] font-normal leading-4 text-[#9293A0] md:mb-1 md:h-12">
                {i18n.t('points_trade_reward_description', {
                  value: milliFormat(
                    pointsWayData.tradeReward.reachStandardValue,
                  ),
                  points: milliFormat(pointsWayData.tradeReward.awardValue),
                })}
              </div>
              <div className="flex h-4 items-center">
                <SvgIconPointsLimit className="mr-1 inline-block size-3.5 p-0.5 md:size-4" />
                <span className="text-center text-[11px] font-normal leading-normal text-[#000] md:text-[12px]">
                  {pointsWayData.tradeReward.limitValue === 0
                    ? i18n.t('points_no_limit')
                    : i18n.t('points_reward_limit', {
                        limit: milliFormat(
                          pointsWayData.tradeReward.limitValue,
                        ),
                      })}
                </span>
              </div>
            </div>

            {/* 成交额卡片 */}
            <div
              className="flex flex-col items-center justify-center rounded-lg px-3 py-3 md:justify-start md:px-4 md:py-5"
              style={{
                backgroundImage: `url(${Card3Img})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
              }}
            >
              <div className="mb-1 mt-0 flex items-center justify-center md:mb-1 md:mt-5">
                <div className="flex items-center">
                  <img
                    src={PointsImg}
                    alt="Points"
                    className="mr-1.5 size-6 md:mr-1 md:size-8"
                  />
                  <span className="text-[22px] font-bold leading-8 text-[#1A1B1E] md:h-8 md:text-[24px]">
                    +{milliFormat(pointsWayData.volumeReward.awardValue)}
                  </span>
                </div>
              </div>
              <div className="mb-1 text-center text-[15px] font-bold text-[#1A1B1E] md:mb-1 md:h-6 md:text-[16px]">
                {i18n.t('points_volume_reward')}
              </div>
              <div className="mb-2 flex min-h-[40px] items-center justify-center text-center text-[12px] font-normal leading-4 text-[#9293A0] md:mb-1 md:h-12">
                {i18n.t('points_volume_reward_description', {
                  value: milliFormat(
                    pointsWayData.volumeReward.reachStandardValue,
                  ),
                  points: milliFormat(pointsWayData.volumeReward.awardValue),
                })}
              </div>
              <div className="flex h-4 items-center">
                <SvgIconPointsLimit className="mr-1 inline-block size-3.5 p-0.5 md:size-4" />
                <span className="text-center text-[11px] font-normal leading-normal text-[#000] md:text-[12px]">
                  {pointsWayData.volumeReward.limitValue === 0
                    ? i18n.t('points_no_limit')
                    : i18n.t('points_reward_limit', {
                        limit: milliFormat(
                          pointsWayData.volumeReward.limitValue,
                        ),
                      })}
                </span>
              </div>
            </div>

            {/* 邀请注册卡片 */}
            <div
              className="flex flex-col items-center justify-center rounded-lg px-3 py-3 md:justify-start md:px-4 md:py-5"
              style={{
                backgroundImage: `url(${Card4Img})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
              }}
            >
              <div className="mb-1 mt-0 flex items-center justify-center md:mb-1 md:mt-5">
                <div className="flex items-center">
                  <img
                    src={PointsImg}
                    alt="Points"
                    className="mr-1.5 size-6 md:mr-2 md:size-8"
                  />
                  <span className="text-[22px] font-bold leading-8 text-[#1A1B1E] md:h-8 md:text-[24px]">
                    +{milliFormat(pointsWayData.inviteReward.awardValue)}
                  </span>
                </div>
              </div>
              <div className="mb-1 text-center text-[15px] font-bold text-[#1A1B1E] md:mb-1 md:h-6 md:text-[16px]">
                {i18n.t('points_invite_new_user')}
              </div>
              <div className="mb-2 flex min-h-[40px] items-center justify-center text-center text-[12px] font-normal leading-4 text-[#9293A0] md:mb-1 md:h-12 md:px-2 md:text-[12px]">
                {i18n.t('points_invite_new_user_description', {
                  value: milliFormat(pointsWayData.inviteReward.awardValue),
                })}
              </div>
              <div className="mb-1 flex h-4 items-center md:mb-1">
                <SvgIconPointsLimit className="mr-1 inline-block size-3.5 p-0.5 md:size-4" />
                <span className="text-center text-[11px] font-normal leading-normal text-[#000] md:text-[12px]">
                  {pointsWayData.inviteReward.limitValue === 0
                    ? i18n.t('points_no_limit')
                    : i18n.t('points_reward_limit', {
                        limit: milliFormat(
                          pointsWayData.inviteReward.limitValue,
                        ),
                      })}
                </span>
              </div>
              <div
                className="flex cursor-pointer items-center rounded-md bg-black px-3 py-0.5 md:px-4 md:py-0"
                onClick={async () => {
                  try {
                    await navigator.clipboard.writeText(inviteLink)
                    message.success(i18n.t('copy_success'))
                  } catch {
                    message.error(i18n.t('copy_failed'))
                  }
                }}
              >
                <SvgIconPointsInviteLink className="mr-1.5 size-2.5 text-white md:mr-2 md:size-3" />
                <span className="h-5 truncate break-all text-[11px] font-medium leading-5 text-white md:text-[12px]">
                  {i18n.t('points_copy_invite_link')}
                </span>
              </div>
            </div>
          </div>
        </Flex>
      </Flex>
    </Flex>
  )
}

Component.displayName = 'PointsPage'
export default Component
