import { NavBarProps } from 'antd-mobile'
import { HeadDesktop } from '@/components/HeadAndFoot/head/HeadDesktop'
import { useDevice } from '@/hooks/useDevice'
import { useEffect } from 'react'
import { useRoute } from '@/hooks/useRoute'

/**
 * @description 移动端 header 组件
 */
export function HeadMobile() {
  return <></>
}

/**
 * @description 头部模块
 */
function HeadModule({
  head,
  headConfig,
}: {
  head: boolean
  headConfig?: NavBarProps
}) {
  const { isMobile } = useDevice()
  const { location } = useRoute()

  useEffect(() => {
    const isChatRoom =
      location.pathname === '/chatroom' || location.pathname === '/trade'
    document.body.style.backgroundColor = isChatRoom ? '#131313' : '#f2f3f7'

    const metaThemeColor = document.querySelector("meta[name='theme-color']")
    const metaMsTileColor = document.querySelector(
      "meta[name='msapplication-TileColor']",
    )

    if (isChatRoom) {
      metaThemeColor?.setAttribute('content', '#131313')
      metaMsTileColor?.setAttribute('content', '#131313')
    } else {
      metaThemeColor?.setAttribute('content', '#f2f3f7')
      metaMsTileColor?.setAttribute('content', '#f2f3f7')
    }

    return () => {
      // 颜色和需要和 style.css body 色值保持一致
      document.body.style.backgroundColor = '#f2f3f7'
      metaThemeColor?.setAttribute('content', '#f2f3f7')
      metaMsTileColor?.setAttribute('content', '#f2f3f7')
    }
  }, [location.pathname])

  return (
    head &&
    (isMobile ? (
      <HeadMobile {...(headConfig || {})} />
    ) : (
      <HeadDesktop {...(headConfig || {})} />
    ))
  )
}

export default HeadModule
