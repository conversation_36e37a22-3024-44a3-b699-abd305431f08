import {
  SvgIconMenuChat,
  SvgIconMenuRoom,
  SvgIconMenuTrade,
  SvgIconMenuUser,
  SvgIconMenuPoints,
  SvgIconTradeLight,
  SvgIconTradeDark,
  SvgIconInvestmentLight,
  SvgIconInvestmentDark,
} from '@/imgs/icons'
import { userStore } from '@/store'
import clsx from 'clsx'

export interface NavItem {
  name: string
  path: string
  Icon: React.FC<{ className?: string; theme?: 'dark' | 'light' }>
}

export const NAVS: NavItem[] = [
  {
    // 语音房
    name: 'home',
    path: '/',
    Icon: ({ theme }) => {
      const isDark = theme === 'dark'
      return (
        <SvgIconMenuRoom
          fill={isDark ? 'white' : 'black'}
          stroke={isDark ? 'black' : 'white'}
          className="size-6"
        />
      )
    },
  },
  {
    // 消息
    name: 'messages',
    path: '/msgs',
    Icon: ({ theme }) => (
      <SvgIconMenuChat
        className={clsx(
          'size-6',
          theme === 'dark' ? 'text-white' : 'text-black',
        )}
      />
    ),
  },
  {
    // 交易
    name: 'trade',
    path: '/trade',
    Icon: ({ theme }) => {
      const isDark = theme === 'dark'
      return isDark ? (
        <SvgIconTradeDark className="size-6" />
      ) : (
        <SvgIconTradeLight className="size-6" />
      )
    },
  },
  // {
  //   // 积分
  //   name: 'points',
  //   path: '/points',
  //   Icon: ({ theme }) => {
  //     const isDark = theme === 'dark'
  //     return (
  //       <SvgIconMenuPoints
  //         fill={isDark ? 'white' : 'black'}
  //         stroke={isDark ? 'black' : 'white'}
  //         className="size-6"
  //       />
  //     )
  //   },
  // },
  {
    // 投资组合
    name: 'investment',
    path: '/investment',
    Icon: ({ theme }) => {
      const isDark = theme === 'dark'
      return isDark ? (
        <SvgIconInvestmentDark className="size-6" />
      ) : (
        <SvgIconInvestmentLight className="size-6" />
      )
    },
  },
  // {
  //   // 应用
  //   name: 'apps',
  //   path: '/apps',
  //   Icon: ({ theme }) => (
  //     <SvgIconMenuTrade
  //       className={clsx(
  //         'size-6',
  //         theme === 'dark' ? 'text-white' : 'text-black',
  //       )}
  //     />
  //   ),
  // },
  {
    // 我的
    name: 'profile',
    path: '/profile',
    Icon: ({ theme }) => {
      return (
        <SvgIconMenuUser
          className={clsx(
            'size-5',
            theme === 'dark' ? 'text-white' : 'text-black',
          )}
        />
      )
    },
  },
]
