import { useRoute } from '@/hooks/useRoute'
import {
  SvgIconLogoMenu,
  SvgIconLogoBeta,
  SvgIconHomeCustomerSevice,
  SvgIconHomeAppGuide,
} from '@/imgs/icons'
import { useSessionStorageState } from 'ahooks'
import { NavItem, NAVS } from '@/components/HeadAndFoot/head/navs'
import { STORAGE_NAV_ISDARK } from '@/utils/STORAGE_REG'
import clsx from 'clsx'
import { useEffect, useState } from 'react'
import { userStore } from '@/store'
import { usePrivy } from '@privy-io/react-auth'
import { useFormError } from '@/hooks/useFormError'
import { NoticeType } from 'antd/es/message/interface'
import { ImgPumpkinNavAvatar } from '@/imgs/other'
import emitter from '@/utils/emitter'
import { ChatroomManagerEvents } from '@/managers/chatroomManager'
import { useAuthLogin } from '@/hooks/useAuthLogin'
import { observer } from 'mobx-react-lite'

declare global {
  interface Window {
    Intercom: any
  }
}

/**
 * @description 桌面端 header 组件
 */
export const HeadDesktop = observer(() => {
  const { contextHolder } = useFormError()
  const [hasNewMsg, setHasNewMsg] = useState(false)

  const { ready, authenticated } = usePrivy()
  const { path, location } = useRoute()
  const profileLogin = useAuthLogin('/profile')
  const messagesLogin = useAuthLogin('/messages')
  const [isDark, setIsDark] = useSessionStorageState(STORAGE_NAV_ISDARK, {
    defaultValue: false,
    listenStorageChange: true,
  })

  useEffect(() => {
    const handler = (flag: boolean) => {
      setHasNewMsg(flag)
    }
    emitter.on(ChatroomManagerEvents.PKMsgUnreadCountChanged, handler)
    return () => {
      emitter.off(ChatroomManagerEvents.PKMsgUnreadCountChanged, handler)
    }
  }, [])

  useEffect(() => {}, [userStore.token])

  useEffect(() => {
    setIsDark(
      location.pathname === '/chatroom' || location.pathname === '/trade',
    )
  }, [location.pathname])

  function toLink(link: string, name: string) {
    if (
      userStore.IS_LOGIN() === false &&
      (name === 'profile' || name === 'messages')
    ) {
      if (ready && authenticated) {
        // 根据不同页面使用对应的登录函数
        if (name === 'profile') {
          profileLogin.logoutAndLogin()
        } else if (name === 'messages') {
          messagesLogin.logoutAndLogin()
        }
        return
      } else if (ready) {
        if (name === 'profile') {
          profileLogin.login()
        } else if (name === 'messages') {
          messagesLogin.login()
        }
        return
      }
    }
    path(link, { query: true })
  }

  return (
    <div
      className={clsx(
        'flex h-screen w-[60px] flex-col items-center border-r',
        isDark
          ? 'border-r-custom-dark bg-custom-dark'
          : 'border-r-custom-light bg-white',
      )}
    >
      {contextHolder}
      <div
        className="relative mb-5 mt-5 cursor-pointer transition-transform duration-300 hover:scale-110"
        onClick={() => toLink('/', '')}
      >
        <SvgIconLogoMenu className="w-10" />
        <SvgIconLogoBeta className="absolute -right-1 -top-2 h-3 w-4" />
      </div>
      <div className="w-full flex-grow">
        {NAVS.map((item: NavItem) => (
          <div
            className={clsx(
              'flex w-full cursor-pointer justify-center align-middle',
              location.pathname === item.path && isDark && 'bg-white/5',
              location.pathname === item.path && !isDark && 'bg-black/5',
            )}
            key={item.name}
            onClick={() => toLink(item.path, item.name)}
          >
            <div className="py-3 transition-transform duration-300 hover:scale-110">
              {(() => {
                // 针对不同的导航项做不同的处理
                if (item.name === 'profile') {
                  // 用户已登录则显示真实头像，否则显示默认图标
                  return authenticated && userStore.IS_LOGIN() ? (
                    <img
                      src={
                        userStore.info?.profile?.photos[0]?.path ||
                        ImgPumpkinNavAvatar
                      }
                      className={clsx(
                        'size-7',
                        'rounded-full',
                        isDark ? 'text-white' : 'text-black',
                      )}
                      alt="avatar"
                    />
                  ) : (
                    <item.Icon theme={isDark ? 'dark' : 'light'} />
                  )
                }
                if (item.name === 'messages') {
                  // “消息”项只显示一个图标，如果检测到有新消息则在图标右上角展示红色小圆点
                  return (
                    <div className="relative">
                      <item.Icon theme={isDark ? 'dark' : 'light'} />
                      {hasNewMsg && userStore.IS_LOGIN() && (
                        <span
                          className="absolute -right-0 -top-0 h-2 w-2 rounded-full bg-red-500"
                          style={{ fontSize: 0 }}
                        />
                      )}
                    </div>
                  )
                }
                // 其他项（包括例如 contacts 等）直接渲染普通图标即可
                return <item.Icon theme={isDark ? 'dark' : 'light'} />
              })()}
            </div>
          </div>
        ))}
      </div>
      {/* 添加新的图标 */}
      <div
        className="transition-transform duration-300 hover:scale-110"
        onClick={() => {
          if (typeof window !== 'undefined' && window.Intercom) {
            window.Intercom('showMessages')
          }
        }}
      >
        <SvgIconHomeCustomerSevice className="size-12 cursor-pointer" />
      </div>
      <div
        className="pb-4 transition-transform duration-300 hover:scale-110"
        onClick={() => {
          window.open('/guide', '_blank')
        }}
      >
        <SvgIconHomeAppGuide className="size-12 cursor-pointer" />
      </div>
    </div>
  )
})
