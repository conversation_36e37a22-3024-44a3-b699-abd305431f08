/**
 * @description 移动端 footer 组件
 */
import { useRoute } from '@/hooks/useRoute'
import { Flex } from 'antd'
import { useEffect, useState } from 'react'
import clsx from 'clsx'
import { userStore } from '@/store'
import { observer } from 'mobx-react-lite'
import emitter from '@/utils/emitter'
import { ChatroomManagerEvents } from '@/managers/chatroomManager'
import { useAuthLogin } from '@/hooks/useAuthLogin'
import { NavItem, NAVS } from '@/components/HeadAndFoot/head/navs'
import { usePrivy } from '@privy-io/react-auth'
import { ImgPumpkinNavAvatar } from '@/imgs/other'
import { useDevice } from '@/hooks/useDevice'
import {
  SvgIconMenuMobileChat,
  SvgIconMenuChatSelected,
  SvgIconMobileHome,
  SvgIconMobileHomeSelected,
  SvgIconMenuPoints,
  SvgIconMenuPointsSelected,
  SvgIconTradeLight,
  SvgIconTradeSelected,
  SvgIconInvestmentLight,
  SvgIconInvestmentSelected,
} from '@/imgs/icons'

const FootMobile = observer(() => {
  const { path, location } = useRoute()

  const [hasNewMsg, setHasNewMsg] = useState(false)
  const { ready, authenticated } = usePrivy()
  const profileLogin = useAuthLogin('/profile')
  const messagesLogin = useAuthLogin('/msgs')
  const { isMobile } = useDevice()

  // 筛选需要在底部导航显示的项目
  const footNavs = NAVS.filter((item) =>
    ['home', 'messages', 'profile', 'trade', 'investment'].includes(item.name),
  )

  useEffect(() => {
    const handler = (flag: boolean) => {
      setHasNewMsg(flag)
    }
    emitter.on(ChatroomManagerEvents.PKMsgUnreadCountChanged, handler)
    return () => {
      emitter.off(ChatroomManagerEvents.PKMsgUnreadCountChanged, handler)
    }
  }, [])

  function toLink(link: string, name: string) {
    if (
      userStore.IS_LOGIN() === false &&
      (name === 'profile' || name === 'messages')
    ) {
      if (ready && authenticated) {
        // 根据不同页面使用对应的登录函数
        if (name === 'profile') {
          profileLogin.logoutAndLogin()
        } else if (name === 'messages') {
          messagesLogin.logoutAndLogin()
        }
        return
      } else if (ready) {
        if (name === 'profile') {
          profileLogin.login()
        } else if (name === 'messages') {
          messagesLogin.login()
        }
        return
      }
    }
    path(link, { query: true })
  }

  if (
    !isMobile ||
    !['/', '/msgs', '/profile', '/wallet', '/trade', '/investment'].includes(
      location.pathname,
    )
  ) {
    return null
  }

  return (
    <footer className="fixed bottom-0 left-0 right-0 z-50 border-t border-gray-100 bg-white">
      <Flex className="h-[60px] w-full items-center justify-around">
        {footNavs.map((item: NavItem) => (
          <Flex
            key={item.name}
            className={clsx(
              'cursor-pointer flex-col items-center',
              location.pathname === item.path ? 'text-black' : 'text-gray-400',
            )}
            onClick={() => toLink(item.path, item.name)}
          >
            <div className="transition-transform duration-300 hover:scale-110">
              {(() => {
                if (item.name === 'profile') {
                  // 用户已登录则显示真实头像，否则显示默认图标
                  return authenticated && userStore.IS_LOGIN() ? (
                    <img
                      src={
                        userStore.info?.profile?.photos[0]?.path ||
                        ImgPumpkinNavAvatar
                      }
                      className="size-7 rounded-full"
                      alt="avatar"
                    />
                  ) : (
                    <item.Icon theme="light" />
                  )
                }
                if (item.name === 'messages') {
                  // "消息"项显示未读消息红点
                  return (
                    <div className="relative">
                      {location.pathname === '/msgs' ? (
                        <SvgIconMenuChatSelected className="size-6" />
                      ) : (
                        <SvgIconMenuMobileChat className="size-6" />
                      )}
                      {hasNewMsg && userStore.IS_LOGIN() && (
                        <span
                          className="absolute -right-0 -top-0 h-2 w-2 rounded-full bg-red-500"
                          style={{ fontSize: 0 }}
                        />
                      )}
                    </div>
                  )
                }
                if (item.name === 'points') {
                  return (
                    <div className="relative">
                      {location.pathname === '/points' ? (
                        <SvgIconMenuPointsSelected className="size-6" />
                      ) : (
                        <SvgIconMenuPoints className="size-6" />
                      )}
                    </div>
                  )
                }
                if (item.name === 'trade') {
                  return (
                    <div className="relative">
                      {location.pathname === '/trade' ? (
                        <SvgIconTradeSelected className="size-6" />
                      ) : (
                        <SvgIconTradeLight className="size-6" />
                      )}
                    </div>
                  )
                }
                if (item.name === 'investment') {
                  return (
                    <div className="relative">
                      {location.pathname === '/investment' ? (
                        <SvgIconInvestmentSelected className="size-6" />
                      ) : (
                        <SvgIconInvestmentLight className="size-6" />
                      )}
                    </div>
                  )
                }
                // 其他导航项直接显示图标
                return (
                  <>
                    {location.pathname === '/' ? (
                      <SvgIconMobileHomeSelected className="size-6" />
                    ) : (
                      <SvgIconMobileHome className="size-6" />
                    )}
                  </>
                )
              })()}
            </div>
          </Flex>
        ))}
      </Flex>
    </footer>
  )
})

/**
 * @description footer 模块
 */
function FootModule({ foot }: { foot: boolean }) {
  return foot && <FootMobile />
}

export default FootModule
