import {
  ConfigProvider,
  Input,
  InputNumber,
  InputNumberProps,
  InputProps,
} from 'antd'
import styled from 'styled-components'
import { SearchOutlined } from '@ant-design/icons'
import tw from 'twin.macro'
import clsx from 'clsx'

const AntdInput = styled(Input)`
  .ant-input-clear-icon {
    ${tw`text-white`}
    &:hover {
      ${tw`text-white`}
    }
  }
`

const AntdInputNumberTarde = styled(InputNumber)`
  input::-webkit-outer-spin-button, input: :-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  input[type='number'] {
    -moz-appearance: textfield;
  }
`

// 使用 formatter 删除末尾的 0
const formatValue = (value: string | number | undefined): string => {
  if (value === undefined || value === null || value === '') {
    return ''
  }

  // 将值转为字符串并删除末尾的0
  let strValue = String(value)
  if (strValue.includes('.')) {
    // 使用正则表达式删除末尾的0
    strValue = strValue.replace(/(\.\d*?)0+$/, '$1')
    // 如果小数点后没有数字，删除小数点
    if (strValue.endsWith('.')) {
      strValue = strValue.slice(0, -1)
    }
  }

  return strValue
}

export const InputNumberTarde = (props: InputNumberProps) => {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#5f606d',
          colorBgContainer: 'inherit',
          colorBorder: '#5f606d',
          colorTextPlaceholder: '#5f606d',
        },
        components: {
          InputNumber: {
            colorText: '#fff',
            activeBg: 'inherit',
            activeBorderColor: '#5f606d',
            activeShadow: 'none',
          },
        },
      }}
    >
      <AntdInputNumberTarde
        controls={false}
        size="large"
        inputMode="decimal"
        stringMode
        formatter={formatValue}
        {...props}
      />
    </ConfigProvider>
  )
}

export const InputSearchTarde = (props: InputProps) => {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#28292f',
          colorBgContainer: '#28292f',
          colorBorder: '#28292f',
          colorTextPlaceholder: '#28292f',
        },
        components: {
          Input: {
            colorText: '#fff',
            activeBg: '#28292f',
            activeBorderColor: '#28292f',
            activeShadow: 'none',
            warningActiveShadow: 'none',
          },
        },
      }}
    >
      <AntdInput
        size="large"
        allowClear
        prefix={<SearchOutlined />}
        {...props}
      />
    </ConfigProvider>
  )
}

const AntdInputNumberPerp = styled(AntdInputNumberTarde)`
  ${tw`border-none`}
  .ant-input-number-group-addon {
    ${tw`ml-0 border-none bg-transparent pl-0`}
  }
  .ant-input-number-outlined {
    ${tw`border-none`}
  }
  .ant-input-number-input {
    ${tw`text-right`}
  }
  .ant-input-number-input-wrap input {
    ${tw`pr-1`}
  }
`

export const InputNumberPerp = (
  props: InputNumberProps & { theme?: 'dark' | 'light' },
) => {
  const { theme, ...rest } = props

  const isLight = theme === 'light'
  const isDark = theme !== 'light'

  const themeConfig = isLight
    ? {
        token: {
          colorPrimary: 'black',
          colorBgContainer: 'inherit',
          colorBorder: 'black',
          colorTextPlaceholder: 'black',
        },
        components: {
          InputNumber: {
            colorText: 'black',
            activeBg: 'inherit',
            activeBorderColor: 'black',
            activeShadow: 'none',
          },
        },
      }
    : {
        token: {
          colorPrimary: '#5f606d',
          colorBgContainer: 'inherit',
          colorBorder: '#5f606d',
          colorTextPlaceholder: '#5f606d',
        },
        components: {
          InputNumber: {
            colorText: '#fff',
            activeBg: 'inherit',
            activeBorderColor: '#5f606d',
            activeShadow: 'none',
          },
        },
      }

  return (
    <ConfigProvider theme={themeConfig}>
      <div
        className={clsx(
          'rounded-8 border transition-all duration-300',
          isDark && 'border-[#5F606D] hover:border-[#F2F3F7]',
          isLight && 'border-[#F2F3F7] hover:border-[#5F606D]',
        )}
      >
        <AntdInputNumberPerp
          controls={false}
          size="large"
          inputMode="decimal"
          stringMode
          formatter={formatValue}
          {...rest}
        />
      </div>
    </ConfigProvider>
  )
}
