import { useMedia } from '@/hooks/useMedia'
import { Popup } from 'antd-mobile'
import React from 'react'
import { SafeAreaApp } from '@/components/SafeAreaApp'
import { SvgIconClose } from '@/imgs/icons'

export function PopupDown({
  title,
  label,
  intro,
  offLine,
  children,
  closeOnMaskClick,
  visible,
  bodyStyle,
  onClose,
}: {
  title?: React.ReactNode
  label?: React.ReactNode
  intro?: React.ReactNode
  line?: boolean
  offLine?: boolean
  children?: React.ReactNode
  closeOnMaskClick?: boolean
  visible: boolean
  onClose: () => void
  bodyStyle?: React.CSSProperties
}) {
  const { isMd } = useMedia()
  return !isMd ? (
    <></>
  ) : (
    <Popup
      visible={visible}
      closeOnMaskClick={closeOnMaskClick}
      closeOnSwipe={true}
      showCloseButton
      onMaskClick={onClose}
      closeIcon={
        <SvgIconClose
          className="size-4 cursor-pointer text-white"
          onClick={onClose}
        />
      }
      bodyStyle={{
        borderTopLeftRadius: '16px',
        borderTopRightRadius: '16px',
        ...(bodyStyle || { backgroundColor: '#1a1b1e' }),
      }}
    >
      {!offLine && <div className="m-auto mb-3 mt-2 h-1.5 w-12 rounded-full" />}
      {(title || label || intro) && (
        <div className="px-4 pb-2">
          {label && (
            <div className="mb-1 text-xl font-bold leading-7 text-white">
              {label}
            </div>
          )}
          {intro && (
            <div className="text-xs font-normal leading-none text-gray-600">
              {intro}
            </div>
          )}
        </div>
      )}
      {children}
      <SafeAreaApp />
    </Popup>
  )
}
